/* DigitSnap App Styles */

:root {
    --primary-yellow: #FFD700;
    --dark-yellow: #FFC107;
    --primary-black: #000000;
    --dark-black: #0D0D0D;
    --light-black: #1A1A1A;
    --gray-black: #2D2D2D;
    --soft-black: #333333;
    --white: #FFFFFF;
    --light-gray: #F5F5F5;
    --medium-gray: #999999;
    --dark-gray: #666666;
    --success: #4CAF50;
    --error: #F44336;
    --warning: #FF9800;
    --info: #2196F3;
    
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --border-radius: 12px;
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    --transition: 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: var(--primary-black);
    color: var(--white);
    overflow-x: hidden;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-black);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    animation: fadeInUp 0.8s ease;
}

.loading-logo {
    width: 80px;
    height: 80px;
    background: var(--primary-yellow);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: var(--primary-black);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-black);
    border-top: 4px solid var(--primary-yellow);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: var(--light-black);
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--gray-black);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 24px;
}

.app-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-yellow);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: var(--gray-black);
    border-radius: 20px;
    font-size: 0.9rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: var(--success);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.account-balance {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.balance-label {
    font-size: 0.8rem;
    color: var(--medium-gray);
}

.balance-amount {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-yellow);
}

.settings-btn {
    background: var(--gray-black);
    border: none;
    color: var(--white);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.settings-btn:hover {
    background: var(--primary-yellow);
    color: var(--primary-black);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 24px;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Dashboard Cards */
.dashboard-card {
    background: var(--light-black);
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-black);
    overflow: hidden;
    transition: var(--transition);
}

.dashboard-card:hover {
    border-color: var(--primary-yellow);
    box-shadow: var(--shadow);
}

.card-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--gray-black);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.1rem;
    font-weight: 600;
}

.card-header i {
    color: var(--primary-yellow);
}

.card-content {
    padding: 24px;
}

/* Market Overview */
.market-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 16px;
    background: var(--gray-black);
    border-radius: 8px;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: var(--medium-gray);
    margin-bottom: 8px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--white);
}

.stat-value.success {
    color: var(--success);
}

.stat-value.profit {
    color: var(--primary-yellow);
}

/* Trading Signals */
.signal-filter select {
    background: var(--gray-black);
    border: 1px solid var(--soft-black);
    color: var(--white);
    padding: 8px 12px;
    border-radius: 6px;
}

.signals-list {
    max-height: 300px;
    overflow-y: auto;
}

.signal-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: var(--gray-black);
    border-radius: 8px;
    margin-bottom: 12px;
    border-left: 4px solid var(--success);
}

.signal-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.signal-asset {
    font-weight: 600;
    color: var(--white);
}

.signal-direction {
    font-size: 0.9rem;
    color: var(--medium-gray);
}

.signal-confidence {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.confidence-high {
    background: var(--success);
    color: var(--white);
}

.confidence-medium {
    background: var(--warning);
    color: var(--white);
}

/* Chart Analysis */
.chart-controls {
    display: flex;
    gap: 12px;
}

.chart-controls select {
    background: var(--gray-black);
    border: 1px solid var(--soft-black);
    color: var(--white);
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 0.9rem;
}

.chart-container {
    height: 300px;
    background: var(--gray-black);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-placeholder {
    text-align: center;
    color: var(--medium-gray);
}

.chart-placeholder i {
    font-size: 3rem;
    margin-bottom: 16px;
    color: var(--primary-yellow);
}

/* No Data States */
.no-signals,
.no-trades {
    text-align: center;
    padding: 40px 20px;
    color: var(--medium-gray);
}

.no-signals i,
.no-trades i {
    font-size: 2.5rem;
    margin-bottom: 16px;
    color: var(--primary-yellow);
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 24px;
    right: 24px;
    width: 56px;
    height: 56px;
    background: var(--primary-yellow);
    color: var(--primary-black);
    border: none;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: var(--shadow);
    transition: var(--transition);
    z-index: 1000;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(255, 215, 0, 0.4);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Trading Panel */
.trading-panel {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: var(--light-black);
    border-left: 1px solid var(--gray-black);
    transition: right 0.3s ease;
    z-index: 1001;
    overflow-y: auto;
}

.trading-panel.active {
    right: 0;
}

.panel-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--gray-black);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.close-panel {
    background: none;
    border: none;
    color: var(--medium-gray);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: var(--transition);
}

.close-panel:hover {
    background: var(--gray-black);
    color: var(--white);
}

.panel-content {
    padding: 24px;
}

.trade-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 500;
    color: var(--white);
}

.form-group input,
.form-group select {
    padding: 12px 16px;
    background: var(--gray-black);
    border: 1px solid var(--soft-black);
    border-radius: 8px;
    color: var(--white);
    font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-yellow);
}

.direction-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.direction-btn {
    padding: 16px;
    border: 2px solid var(--gray-black);
    border-radius: 8px;
    background: var(--gray-black);
    color: var(--white);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.direction-btn:hover {
    border-color: var(--primary-yellow);
}

.direction-btn.active {
    border-color: var(--primary-yellow);
    background: rgba(255, 215, 0, 0.1);
}

.call-btn.active {
    border-color: var(--success);
    background: rgba(76, 175, 80, 0.1);
}

.put-btn.active {
    border-color: var(--error);
    background: rgba(244, 67, 54, 0.1);
}

.execute-trade-btn {
    padding: 16px;
    background: var(--primary-yellow);
    color: var(--primary-black);
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.execute-trade-btn:hover {
    background: var(--dark-yellow);
    transform: translateY(-2px);
}

/* Notifications */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.notification {
    padding: 16px 20px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 300px;
    box-shadow: var(--shadow);
    animation: slideInRight 0.3s ease;
}

.notification-success {
    background: var(--success);
    color: var(--white);
}

.notification-error {
    background: var(--error);
    color: var(--white);
}

.notification-info {
    background: var(--info);
    color: var(--white);
}

.notification-warning {
    background: var(--warning);
    color: var(--white);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .main-content {
        padding: 16px;
    }

    .app-header {
        padding: 12px 16px;
    }

    .market-stats {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .header-right {
        gap: 12px;
    }

    .account-balance {
        display: none;
    }

    .trading-panel {
        width: 100%;
        right: -100%;
    }

    .notification {
        min-width: auto;
        width: calc(100vw - 40px);
    }

    .notification-container {
        left: 20px;
        right: 20px;
    }
}
