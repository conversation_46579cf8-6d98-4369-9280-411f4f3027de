<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Derivlite - Advanced Trading Platform & Tools</title>
    <meta name="description" content="Derivlite offers cutting-edge trading tools and platform with 95% accuracy. Experience DigitSnap and transform your trading journey.">

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="digitsnap/icons/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="digitsnap/icons/icon-16x16.png">
    <link rel="apple-touch-icon" sizes="192x192" href="digitsnap/icons/icon-192x192.png">
    <link rel="apple-touch-icon" sizes="152x152" href="digitsnap/icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="144x144" href="digitsnap/icons/icon-144x144.png">
    <link rel="apple-touch-icon" sizes="128x128" href="digitsnap/icons/icon-128x128.png">
    <link rel="apple-touch-icon" sizes="96x96" href="digitsnap/icons/icon-96x96.png">
    <link rel="apple-touch-icon" sizes="72x72" href="digitsnap/icons/icon-72x72.png">

    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>

<body>
    <!-- Social Media Popup -->
    <div id="socialsPopup" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; z-index: 10000; background: rgba(0, 0, 0, 0.8); backdrop-filter: blur(5px);">
        <div style="position: relative; background: #1a1a1a; border: 2px solid #ffd700; border-radius: 15px; width: 90%; max-width: 500px; max-height: 90vh; overflow: hidden; animation: popupSlideIn 0.3s ease-out;">
            <style>
                @keyframes popupSlideIn {
                    from {
                        opacity: 0;
                        transform: scale(0.8) translateY(-20px);
                    }
                    to {
                        opacity: 1;
                        transform: scale(1) translateY(0);
                    }
                }

                @media (max-width: 768px) {
                    #socialsPopup>div {
                        width: 95%;
                        margin: 10px;
                    }
                    .popup-social-links {
                        grid-template-columns: 1fr !important;
                        gap: 12px !important;
                    }
                    .popup-social-link {
                        padding: 12px 15px !important;
                        font-size: 0.95rem !important;
                    }
                }
            </style>

            <div style="background: linear-gradient(135deg, #ffd700, #ffc107); color: #000; padding: 20px; display: flex; justify-content: space-between; align-items: center;">
                <h3 style="font-size: 1.5rem; font-weight: 700; margin: 0;">Connect with DerivLite</h3>
                <button onclick="document.getElementById('socialsPopup').style.display='none'; document.body.style.overflow='auto';" style="background: none; border: none; color: #000; font-size: 1.2rem; cursor: pointer; padding: 5px; border-radius: 50%; width: 35px; height: 35px; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;"
                    onmouseover="this.style.background='rgba(0,0,0,0.1)'; this.style.transform='rotate(90deg)'" onmouseout="this.style.background='none'; this.style.transform='rotate(0deg)'">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div style="padding: 30px; color: white;">
                <p style="text-align: center; margin-bottom: 25px; font-size: 1.1rem; color: #f5f5f5;">Stay connected with us through our official channels:</p>

                <div class="popup-social-links" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 30px;">
                    <a href="https://wa.me/16134957545" target="_blank" class="popup-social-link" style="display: flex; align-items: center; gap: 12px; padding: 15px 20px; background: #2d2d2d; border: 2px solid #25D366; border-radius: 10px; color: white; text-decoration: none; transition: all 0.3s ease; font-weight: 500; width: 100%; justify-content: flex-start;"
                        onmouseover="this.style.transform='translateY(-2px)'; this.style.background='linear-gradient(135deg, #25D366, #128C7E)'; this.style.boxShadow='0 8px 25px rgba(255, 215, 0, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.background='#2d2d2d'; this.style.boxShadow='none'">
                        <i class="fab fa-whatsapp" style="font-size: 1.5rem; width: 24px; text-align: center; flex-shrink: 0;"></i>
                        <span style="font-size: 1rem; white-space: nowrap;">WhatsApp</span>
                    </a>

                    <a href="https://t.me/binary_blueprint" target="_blank" class="popup-social-link" style="display: flex; align-items: center; gap: 12px; padding: 15px 20px; background: #2d2d2d; border: 2px solid #0088CC; border-radius: 10px; color: white; text-decoration: none; transition: all 0.3s ease; font-weight: 500; width: 100%; justify-content: flex-start;"
                        onmouseover="this.style.transform='translateY(-2px)'; this.style.background='linear-gradient(135deg, #0088CC, #005577)'; this.style.boxShadow='0 8px 25px rgba(255, 215, 0, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.background='#2d2d2d'; this.style.boxShadow='none'">
                        <i class="fab fa-telegram" style="font-size: 1.5rem; width: 24px; text-align: center; flex-shrink: 0;"></i>
                        <span style="font-size: 1rem; white-space: nowrap;">Telegram</span>
                    </a>

                    <a href="https://instagram.com/derivlite.com_" target="_blank" class="popup-social-link" style="display: flex; align-items: center; gap: 12px; padding: 15px 20px; background: #2d2d2d; border: 2px solid #E4405F; border-radius: 10px; color: white; text-decoration: none; transition: all 0.3s ease; font-weight: 500; width: 100%; justify-content: flex-start;"
                        onmouseover="this.style.transform='translateY(-2px)'; this.style.background='linear-gradient(135deg, #E4405F, #C13584, #833AB4)'; this.style.boxShadow='0 8px 25px rgba(255, 215, 0, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.background='#2d2d2d'; this.style.boxShadow='none'">
                        <i class="fab fa-instagram" style="font-size: 1.5rem; width: 24px; text-align: center; flex-shrink: 0;"></i>
                        <span style="font-size: 1rem; white-space: nowrap;">Instagram</span>
                    </a>

                    <a href="mailto:<EMAIL>" class="popup-social-link" style="display: flex; align-items: center; gap: 12px; padding: 15px 20px; background: #2d2d2d; border: 2px solid #ffd700; border-radius: 10px; color: white; text-decoration: none; transition: all 0.3s ease; font-weight: 500; width: 100%; justify-content: flex-start;"
                        onmouseover="this.style.transform='translateY(-2px)'; this.style.background='linear-gradient(135deg, #ffd700, #ffc107)'; this.style.color='#000'; this.style.boxShadow='0 8px 25px rgba(255, 215, 0, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.background='#2d2d2d'; this.style.color='white'; this.style.boxShadow='none'">
                        <i class="fas fa-envelope" style="font-size: 1.5rem; width: 24px; text-align: center; flex-shrink: 0;"></i>
                        <span style="font-size: 1rem; white-space: nowrap;">Email</span>
                    </a>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button onclick="document.getElementById('socialsPopup').style.display='none'; document.body.style.overflow='auto';" style="background: #ffd700; color: #000; border: none; padding: 12px 30px; font-size: 1rem; border-radius: 8px; cursor: pointer; font-weight: 600; transition: transform 0.3s ease;"
                        onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                        Continue to Website
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Header/Navigation -->
    <header class="header">
        <nav class="nav">
            <div class="nav-container">
                <div class="nav-brand">
                    <img src="logo.svg" alt="Derivlite" class="logo">
                    <span class="brand-text">Derivlite</span>
                </div>
                <ul class="nav-menu">
                    <li><a href="#home" class="nav-link">Home</a></li>
                    <li><a href="#digitsnap" class="nav-link">DigitSnap</a></li>
                    <li><a href="./project-x/" class="nav-link">Project X</a></li>
                    <li><a href="#tools" class="nav-link">Tools</a></li>
                    <li><a href="#platform" class="nav-link">Platform</a></li>
                    <li><a href="#features" class="nav-link">Features</a></li>
                    <li><a href="#download" class="nav-link">Download</a></li>
                    <li><a href="#contact" class="nav-link">Contact</a></li>
                    <div class="nav-actions mobile-nav-actions">
                        <a href="#get-started" class="btn btn-primary">Get Started</a>
                    </div>
                </ul>
                <div class="nav-actions">
                    <a href="#get-started" class="btn btn-primary">Get Started</a>
                </div>
                <div class="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">
                        Generate
                        <span class="highlight">5k</span> to
                        <span class="highlight">20k</span> Monthly
                    </h1>
                    <p class="hero-description">
                        Transform your trading with DigitSnap - the most accurate trading tool in the market. Join thousands of successful traders achieving consistent profits with our flagship $250 tool.
                    </p>
                    <div class="hero-stats">
                        <div class="stat">
                            <span class="stat-number">95%</span>
                            <span class="stat-label">Accuracy Rate</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">300+</span>
                            <span class="stat-label">Active Traders</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">24/7</span>
                            <span class="stat-label">Support</span>
                        </div>
                    </div>
                    <div class="hero-actions">
                        <a href="./digitsnap/" class="btn btn-primary btn-lg">
                            <i class="fas fa-bullseye"></i>
                            Get DigitSnap - $250
                        </a>
                        <a href="#digitsnap" class="btn btn-outline btn-lg">
                            <i class="fas fa-info-circle"></i>
                            Learn More
                        </a>
                    </div>
                    <div class="project-x-banner">
                        <div class="banner-icon">🚀</div>
                        <div class="banner-content">
                            <strong>NEW: Project X Millionaire Program</strong>
                            <span>Limited to 15 people only • Oct-Dec 2025</span>
                        </div>
                        <a href="./project-x/" class="banner-cta">Learn More</a>
                    </div>
                </div>
                <div class="hero-visual">
                    <div class="trading-dashboard">
                        <div class="dashboard-header">
                            <div class="dashboard-title">DigitSnap Dashboard</div>
                            <div class="status-indicator active">Live</div>
                        </div>
                        <div class="chart-container">
                            <div class="chart-placeholder">
                                <div class="chart-line"></div>
                                <div class="chart-points">
                                    <div class="point profit"></div>
                                    <div class="point profit"></div>
                                    <div class="point profit"></div>
                                    <div class="point loss"></div>
                                    <div class="point profit"></div>
                                </div>
                            </div>
                        </div>
                        <div class="accuracy-display">
                            <div class="accuracy-circle">
                                <span class="accuracy-number">95%</span>
                                <span class="accuracy-label">Accuracy</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="hero-background">
            <div class="bg-pattern"></div>
        </div>
    </section>

    <!-- DigitSnap Section -->
    <section id="digitsnap" class="digitsnap">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">DigitSnap - Your Trading Edge</h2>
                <p class="section-subtitle">The $250 investment that transforms your trading forever</p>
            </div>
            <div class="digitsnap-content">
                <div class="digitsnap-info">
                    <div class="pricing-highlight">
                        <div class="price-tag">
                            <span class="price-amount">$250</span>
                            <span class="price-label">One-time Payment</span>
                        </div>
                        <div class="value-proposition">
                            <div class="roi-stat">
                                <span class="roi-number">300%+</span>
                                <span class="roi-label">Average ROI in First Month</span>
                            </div>
                        </div>
                    </div>
                    <h3>The Most Profitable $250 You'll Ever Spend</h3>
                    <p>DigitSnap pays for itself within days. With 95% accuracy and proven results, our clients typically recover their investment in the first week of trading.</p>

                    <div class="features-list">
                        <div class="feature-item">
                            <i class="fas fa-bullseye"></i>
                            <div>
                                <h4>95% Accuracy Rate</h4>
                                <p>Industry-leading precision in trade predictions</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-bolt"></i>
                            <div>
                                <h4>Real-time Analysis</h4>
                                <p>Instant market data processing and signals</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-shield-alt"></i>
                            <div>
                                <h4>Risk Management</h4>
                                <p>Advanced algorithms to minimize losses</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-chart-line"></i>
                            <div>
                                <h4>Smart Predictions</h4>
                                <p>AI-powered market trend analysis</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="digitsnap-demo">
                    <div class="demo-container">
                        <div class="demo-screen">
                            <div class="demo-header">
                                <div class="demo-title">DigitSnap Live</div>
                                <div class="demo-status">
                                    <div class="status-dot"></div>
                                    Connected
                                </div>
                            </div>
                            <div class="demo-metrics">
                                <div class="metric">
                                    <span class="metric-label">Today's Accuracy</span>
                                    <span class="metric-value">96.2%</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Profit Trades</span>
                                    <span class="metric-value">847</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Active Signals</span>
                                    <span class="metric-value">12</span>
                                </div>
                            </div>
                            <div class="demo-signals">
                                <div class="signal profit">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>EUR/USD Call - 98% confidence</span>
                                </div>
                                <div class="signal profit">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>GBP/JPY Put - 95% confidence</span>
                                </div>
                                <div class="signal profit">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>AUD/CAD Call - 97% confidence</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Project X Section -->
    <section id="project-x" class="project-x">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Project X - Millionaire Project</h2>
                <p class="section-subtitle">Limited to ONLY 15 People - Transform $900 into $64,000</p>
            </div>
            <div class="project-x-content">
                <div class="project-x-hero">
                    <div class="project-x-badge">
                        <span class="badge-text">EXCLUSIVE</span>
                        <span class="badge-limit">ONLY 15 SPOTS</span>
                    </div>
                    <div class="project-timeline">
                        <div class="timeline-item">
                            <div class="timeline-date">October 1st</div>
                            <div class="timeline-desc">Project Launch</div>
                        </div>
                        <div class="timeline-arrow">→</div>
                        <div class="timeline-item">
                            <div class="timeline-date">December 31st</div>
                            <div class="timeline-desc">Project Completion</div>
                        </div>
                    </div>
                    <div class="transformation-stats">
                        <div class="stat-card">
                            <div class="stat-label">Starting Capital</div>
                            <div class="stat-value">$900</div>
                        </div>
                        <div class="transformation-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="stat-card highlighted">
                            <div class="stat-label">Target Goal</div>
                            <div class="stat-value">$64,000</div>
                        </div>
                    </div>
                </div>
                <div class="project-x-details">
                    <h3>The Most Exclusive Trading Program</h3>
                    <p class="emphasis-text">This is NOT for everyone. Project X is designed for serious traders who are ready to commit to a 3-month intensive program.</p>

                    <div class="project-features">
                        <div class="feature-highlight">
                            <i class="fas fa-users"></i>
                            <div>
                                <h4>Limited to 15 People ONLY</h4>
                                <p>Exclusive access ensures personalized attention and maximum results for each participant</p>
                            </div>
                        </div>
                        <div class="feature-highlight">
                            <i class="fas fa-calendar-alt"></i>
                            <div>
                                <h4>3-Month Intensive Program</h4>
                                <p>October 1st - December 31st, 2025. Complete transformation in 92 days</p>
                            </div>
                        </div>
                        <div class="feature-highlight">
                            <i class="fas fa-chart-line"></i>
                            <div>
                                <h4>7,111% ROI Target</h4>
                                <p>Turn your $900 investment into $64,000 through advanced trading strategies</p>
                            </div>
                        </div>
                        <div class="feature-highlight">
                            <i class="fas fa-crown"></i>
                            <div>
                                <h4>VIP Support & Mentoring</h4>
                                <p>Direct access to our top traders and personalized guidance throughout the program</p>
                            </div>
                        </div>
                    </div>

                    <div class="urgency-box">
                        <div class="urgency-header">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>EXTREMELY LIMITED AVAILABILITY</span>
                        </div>
                        <p>Only <strong>15 spots</strong> available. Once filled, the opportunity is gone forever. Don't miss your chance to join the millionaire project.</p>
                    </div>

                    <div class="project-x-cta">
                        <a href="./project-x/" class="btn btn-primary btn-xl project-x-btn">
                            <i class="fas fa-rocket"></i>
                            Apply for Project X
                            <span class="btn-subtitle">Limited to 15 People</span>
                        </a>
                        <p class="cta-note">Application required. Not guaranteed acceptance.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tools Section -->
    <section id="tools" class="tools">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Trading Tools</h2>
                <p class="section-subtitle">Professional-grade tools for serious traders</p>
            </div>
            <div class="tools-grid">
                <div class="tool-card">
                    <div class="tool-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Advanced Charts</h3>
                    <p>Professional charting tools with multiple timeframes and technical indicators.</p>
                    <div class="tool-features">
                        <span>Multiple Timeframes</span>
                        <span>50+ Indicators</span>
                        <span>Custom Alerts</span>
                    </div>
                </div>
                <div class="tool-card featured">
                    <div class="tool-badge">Best Seller - $250</div>
                    <div class="tool-icon">
                        <i class="fas fa-target"></i>
                    </div>
                    <h3>DigitSnap</h3>
                    <p>Our flagship tool with 95% accuracy rate for precise trading signals. Pay once, profit forever.</p>
                    <div class="tool-features">
                        <span>95% Accuracy</span>
                        <span>Real-time Signals</span>
                        <span>AI-Powered</span>
                        <span>$250 One-time</span>
                    </div>
                    <div class="tool-cta">
                        <a href="./digitsnap/" class="btn btn-primary">
                            <i class="fas fa-shopping-cart"></i>
                            Buy Now - $250
                        </a>
                    </div>
                </div>
                <div class="tool-card">
                    <div class="tool-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3>Auto Trading</h3>
                    <p>Automated trading bots that execute trades based on predefined strategies.</p>
                    <div class="tool-features">
                        <span>24/7 Trading</span>
                        <span>Custom Strategies</span>
                        <span>Risk Controls</span>
                    </div>
                </div>
                <div class="tool-card">
                    <div class="tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <h3>Risk Calculator</h3>
                    <p>Calculate position sizes and manage risk with our advanced calculator.</p>
                    <div class="tool-features">
                        <span>Position Sizing</span>
                        <span>Risk Assessment</span>
                        <span>Portfolio Analysis</span>
                    </div>
                </div>
                <div class="tool-card">
                    <div class="tool-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <h3>Smart Alerts</h3>
                    <p>Get notified instantly when market conditions meet your criteria.</p>
                    <div class="tool-features">
                        <span>Price Alerts</span>
                        <span>Pattern Recognition</span>
                        <span>Multi-platform</span>
                    </div>
                </div>
                <div class="tool-card">
                    <div class="tool-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h3>Analytics</h3>
                    <p>Comprehensive trading performance analysis and reporting tools.</p>
                    <div class="tool-features">
                        <span>Performance Metrics</span>
                        <span>Trade History</span>
                        <span>Custom Reports</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Platform Section -->
    <section id="platform" class="platform">
        <div class="container">
            <div class="platform-content">
                <div class="platform-info">
                    <h2>All-in-One Trading Platform</h2>
                    <p>Access our complete suite of trading tools through our powerful platform at bot.derivlite.com</p>

                    <div class="platform-features">
                        <div class="platform-feature">
                            <i class="fas fa-desktop"></i>
                            <div>
                                <h4>Web Platform</h4>
                                <p>Trade directly from your browser with our responsive web platform</p>
                            </div>
                        </div>
                        <div class="platform-feature">
                            <i class="fas fa-mobile-alt"></i>
                            <div>
                                <h4>Mobile Ready</h4>
                                <p>Full functionality on mobile devices for trading on the go</p>
                            </div>
                        </div>
                        <div class="platform-feature">
                            <i class="fas fa-cloud"></i>
                            <div>
                                <h4>Cloud-Based</h4>
                                <p>Access your account and tools from anywhere in the world</p>
                            </div>
                        </div>
                    </div>

                    <a href="https://bot.derivlite.com" class="btn btn-primary btn-lg">
                        <i class="fas fa-external-link-alt"></i>
                        Access Platform
                    </a>
                </div>
                <div class="platform-preview">
                    <div class="platform-screen">
                        <div class="screen-header">
                            <div class="screen-controls">
                                <div class="control red"></div>
                                <div class="control yellow"></div>
                                <div class="control green"></div>
                            </div>
                            <div class="screen-url">bot.derivlite.com</div>
                        </div>
                        <div class="screen-content">
                            <div class="platform-dashboard">
                                <div class="dashboard-sidebar">
                                    <div class="sidebar-item active"><a href="https://bot.derivlite.com">Dashboard</a></div>
                                    <div class="sidebar-item"><a href="#digitsnap">DigitSnap</a></div>
                                    <div class="sidebar-item">DigitSnap</div>
                                    <div class="sidebar-item">Charts</div>
                                    <div class="sidebar-item">History</div>
                                </div>
                                <div class="dashboard-main">
                                    <div class="dashboard-cards">
                                        <div class="dashboard-card">
                                            <div class="card-title">Total Profit</div>
                                            <div class="card-value profit">+$12,450</div>
                                        </div>
                                        <div class="dashboard-card">
                                            <div class="card-title">Win Rate</div>
                                            <div class="card-value">95.2%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Why Choose Derivlite?</h2>
                <p class="section-subtitle">The complete trading solution for modern traders</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-number">01</div>
                    <h3>Unmatched Accuracy</h3>
                    <p>Our DigitSnap tool delivers 95% accuracy, setting the industry standard for trading precision.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-number">02</div>
                    <h3>Real-time Execution</h3>
                    <p>Lightning-fast order execution with minimal latency for optimal trade entry and exit points.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-number">03</div>
                    <h3>Advanced Security</h3>
                    <p>Bank-level security protocols protect your funds and personal information at all times.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-number">04</div>
                    <h3>24/7 Support</h3>
                    <p>Round-the-clock customer support to assist you whenever you need help with your trading.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-number">05</div>
                    <h3>User-Friendly</h3>
                    <p>Intuitive interface designed for both beginners and professional traders.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-number">06</div>
                    <h3>Continuous Innovation</h3>
                    <p>Regular updates and new features to keep you ahead in the trading game.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section -->
    <section id="download" style="background: #000; padding: 80px 0; color: white;">
        <style>
            @media (max-width: 768px) {
                #download .download-grid {
                    grid-template-columns: 1fr !important;
                    gap: 30px !important;
                }
                #download .download-card {
                    padding: 30px 20px !important;
                }
                #download .stats-grid {
                    grid-template-columns: 1fr !important;
                    gap: 20px !important;
                }
            }
        </style>
        <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
            <div style="text-align: center; margin-bottom: 50px;">
                <h2 style="font-size: 2.5rem; color: white; margin-bottom: 16px;">Get DigitSnap</h2>
                <p style="font-size: 1.2rem; color: #999; max-width: 600px; margin: 0 auto;">Choose your preferred way to access our professional trading platform</p>
            </div>

            <div class="download-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; max-width: 900px; margin: 0 auto;">
                <div class="download-card" style="background: #1a1a1a; border: 2px solid #ffd700; border-radius: 16px; padding: 40px 30px; text-align: center; position: relative; transition: transform 0.3s ease;">
                    <div style="position: absolute; top: -2px; right: -2px; background: #ffd700; color: #000; padding: 6px 12px; font-size: 0.7rem; font-weight: 700; border-radius: 0 16px 0 16px;">RECOMMENDED</div>
                    <div style="width: 70px; height: 70px; background: #ffd700; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px;">
                        <i class="fas fa-download" style="font-size: 1.8rem; color: #000;"></i>
                    </div>
                    <h3 style="font-size: 1.5rem; color: white; margin-bottom: 16px;">Progressive Web App</h3>
                    <p style="color: #999; margin-bottom: 24px;">Install DigitSnap for the complete app experience with offline capabilities</p>

                    <ul style="list-style: none; padding: 0; margin: 0 0 30px 0;">
                        <li style="display: flex; align-items: center; justify-content: center; gap: 8px; margin-bottom: 8px; color: #999;">
                            <i class="fas fa-check" style="color: #ffd700;"></i> Works offline
                        </li>
                        <li style="display: flex; align-items: center; justify-content: center; gap: 8px; margin-bottom: 8px; color: #999;">
                            <i class="fas fa-check" style="color: #ffd700;"></i> Desktop & mobile
                        </li>
                        <li style="display: flex; align-items: center; justify-content: center; gap: 8px; margin-bottom: 8px; color: #999;">
                            <i class="fas fa-check" style="color: #ffd700;"></i> Push notifications
                        </li>
                        <li style="display: flex; align-items: center; justify-content: center; gap: 8px; margin-bottom: 8px; color: #999;">
                            <i class="fas fa-check" style="color: #ffd700;"></i> Auto updates
                        </li>
                    </ul>

                    <div>
                        <a href="./digitsnap/" style="background: #ffd700; color: #000; padding: 14px 24px; border-radius: 10px; text-decoration: none; display: block; font-weight: 600; margin-bottom: 8px; transition: transform 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'"
                            onmouseout="this.style.transform='translateY(0)'">
                            <i class="fas fa-mobile-alt"></i> Install DigitSnap PWA
                        </a>
                        <small style="color: #999; font-style: italic;">Opens with install prompt</small>
                    </div>
                </div>

                <div class="download-card" style="background: #1a1a1a; border: 2px solid rgba(255, 215, 0, 0.1); border-radius: 16px; padding: 40px 30px; text-align: center; transition: transform 0.3s ease;">
                    <div style="width: 70px; height: 70px; background: #ffd700; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px;">
                        <i class="fas fa-globe" style="font-size: 1.8rem; color: #000;"></i>
                    </div>
                    <h3 style="font-size: 1.5rem; color: white; margin-bottom: 16px;">Web Application</h3>
                    <p style="color: #999; margin-bottom: 24px;">Quick access through your browser without any installation required</p>

                    <ul style="list-style: none; padding: 0; margin: 0 0 30px 0;">
                        <li style="display: flex; align-items: center; justify-content: center; gap: 8px; margin-bottom: 8px; color: #999;">
                            <i class="fas fa-check" style="color: #ffd700;"></i> No installation
                        </li>
                        <li style="display: flex; align-items: center; justify-content: center; gap: 8px; margin-bottom: 8px; color: #999;">
                            <i class="fas fa-check" style="color: #ffd700;"></i> Cross-platform
                        </li>
                        <li style="display: flex; align-items: center; justify-content: center; gap: 8px; margin-bottom: 8px; color: #999;">
                            <i class="fas fa-check" style="color: #ffd700;"></i> Always updated
                        </li>
                    </ul>

                    <div>
                        <a href="./digitsnap/" target="_blank" style="background: transparent; color: #ffd700; border: 2px solid #ffd700; padding: 14px 24px; border-radius: 10px; text-decoration: none; display: block; font-weight: 600; transition: transform 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'"
                            onmouseout="this.style.transform='translateY(0)'">
                            <i class="fas fa-external-link-alt"></i> Launch Web App
                        </a>
                    </div>
                </div>
            </div>

            <div class="stats-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 30px; margin: 50px auto; max-width: 600px;">
                <div style="text-align: center; background: rgba(255, 215, 0, 0.05); border: 1px solid rgba(255, 215, 0, 0.1); border-radius: 12px; padding: 20px 15px;">
                    <div style="font-size: 1.8rem; font-weight: 700; color: #ffd700; margin-bottom: 8px;">95%</div>
                    <div style="font-size: 0.9rem; color: #999;">Accuracy</div>
                </div>
                <div style="text-align: center; background: rgba(255, 215, 0, 0.05); border: 1px solid rgba(255, 215, 0, 0.1); border-radius: 12px; padding: 20px 15px;">
                    <div style="font-size: 1.8rem; font-weight: 700; color: #ffd700; margin-bottom: 8px;">300+</div>
                    <div style="font-size: 0.9rem; color: #999;">Users</div>
                </div>
                <div style="text-align: center; background: rgba(255, 215, 0, 0.05); border: 1px solid rgba(255, 215, 0, 0.1); border-radius: 12px; padding: 20px 15px;">
                    <div style="font-size: 1.8rem; font-weight: 700; color: #ffd700; margin-bottom: 8px;">24/7</div>
                    <div style="font-size: 0.9rem; color: #999;">Support</div>
                </div>
            </div>

            <!-- Supported Platforms Section -->
            <div style="text-align: center; margin-top: 40px;">
                <style>
                    @keyframes scrollPlatforms {
                        0% {
                            transform: translateX(0);
                        }
                        100% {
                            transform: translateX(-50%);
                        }
                    }

                    .platforms-track {
                        display: flex;
                        gap: 20px;
                        animation: scrollPlatforms 20s linear infinite;
                        width: max-content;
                    }

                    .platforms-track:hover {
                        animation-play-state: paused;
                    }

                    .platform {
                        background: rgba(255, 215, 0, 0.05);
                        border: 1px solid rgba(255, 215, 0, 0.1);
                        border-radius: 8px;
                        padding: 12px 16px;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        gap: 6px;
                        transition: all 0.3s ease;
                        min-width: 70px;
                        flex-shrink: 0;
                    }

                    .platform:hover {
                        background: rgba(255, 215, 0, 0.1);
                        border-color: rgba(255, 215, 0, 0.2);
                        transform: translateY(-2px);
                    }
                </style>
                <h4 style="font-size: 1.2rem; font-weight: 600; color: white; margin-bottom: 20px;">Supported Platforms</h4>
                <div style="overflow: hidden; width: 100%; position: relative; mask: linear-gradient(to right, transparent 0%, black 15%, black 85%, transparent 100%); -webkit-mask: linear-gradient(to right, transparent 0%, black 15%, black 85%, transparent 100%);">
                    <div class="platforms-track">
                        <div class="platform">
                            <i class="fab fa-chrome" style="font-size: 1.3rem; color: #ffd700;"></i>
                            <span style="font-size: 0.8rem; color: #999; font-weight: 500;">Chrome</span>
                        </div>
                        <div class="platform">
                            <i class="fab fa-firefox" style="font-size: 1.3rem; color: #ffd700;"></i>
                            <span style="font-size: 0.8rem; color: #999; font-weight: 500;">Firefox</span>
                        </div>
                        <div class="platform">
                            <i class="fab fa-safari" style="font-size: 1.3rem; color: #ffd700;"></i>
                            <span style="font-size: 0.8rem; color: #999; font-weight: 500;">Safari</span>
                        </div>
                        <div class="platform">
                            <i class="fab fa-edge" style="font-size: 1.3rem; color: #ffd700;"></i>
                            <span style="font-size: 0.8rem; color: #999; font-weight: 500;">Edge</span>
                        </div>
                        <div class="platform">
                            <i class="fab fa-android" style="font-size: 1.3rem; color: #ffd700;"></i>
                            <span style="font-size: 0.8rem; color: #999; font-weight: 500;">Android</span>
                        </div>
                        <div class="platform">
                            <i class="fab fa-apple" style="font-size: 1.3rem; color: #ffd700;"></i>
                            <span style="font-size: 0.8rem; color: #999; font-weight: 500;">iOS</span>
                        </div>
                        <!-- Duplicate for smooth infinite scroll -->
                        <div class="platform">
                            <i class="fab fa-chrome" style="font-size: 1.3rem; color: #ffd700;"></i>
                            <span style="font-size: 0.8rem; color: #999; font-weight: 500;">Chrome</span>
                        </div>
                        <div class="platform">
                            <i class="fab fa-firefox" style="font-size: 1.3rem; color: #ffd700;"></i>
                            <span style="font-size: 0.8rem; color: #999; font-weight: 500;">Firefox</span>
                        </div>
                        <div class="platform">
                            <i class="fab fa-safari" style="font-size: 1.3rem; color: #ffd700;"></i>
                            <span style="font-size: 0.8rem; color: #999; font-weight: 500;">Safari</span>
                        </div>
                        <div class="platform">
                            <i class="fab fa-edge" style="font-size: 1.3rem; color: #ffd700;"></i>
                            <span style="font-size: 0.8rem; color: #999; font-weight: 500;">Edge</span>
                        </div>
                        <div class="platform">
                            <i class="fab fa-android" style="font-size: 1.3rem; color: #ffd700;"></i>
                            <span style="font-size: 0.8rem; color: #999; font-weight: 500;">Android</span>
                        </div>
                        <div class="platform">
                            <i class="fab fa-apple" style="font-size: 1.3rem; color: #ffd700;"></i>
                            <span style="font-size: 0.8rem; color: #999; font-weight: 500;">iOS</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section id="get-started" class="cta">
        <div class="container">
            <div class="cta-content">
                <h2>Ready to Get DigitSnap?</h2>
                <p>Join thousands of successful traders using the most accurate trading tool available</p>
                <div class="cta-pricing">
                    <div class="cta-price-box">
                        <div class="cta-price">$250</div>
                        <div class="cta-price-label">One-time payment</div>
                        <div class="cta-guarantee">30-day money-back guarantee</div>
                    </div>
                </div>
                <div class="cta-actions">
                    <a href="./digitsnap/" class="btn btn-primary btn-xl">
                        <i class="fas fa-bullseye"></i>
                        Purchase DigitSnap Now
                    </a>
                    <div class="cta-info">
                        <div class="cta-point">
                            <i class="fas fa-check"></i>
                            <span>Instant access</span>
                        </div>
                        <div class="cta-point">
                            <i class="fas fa-check"></i>
                            <span>95% accuracy guaranteed</span>
                        </div>
                        <div class="cta-point">
                            <i class="fas fa-check"></i>
                            <span>24/7 support included</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact" class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <img src="logo.svg" alt="Derivlite" class="footer-logo">
                        <span class="footer-brand-text">Derivlite</span>
                    </div>
                    <p>Advanced trading platform with cutting-edge tools for professional traders.</p>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-telegram"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Platform</h4>
                    <ul>
                        <li><a href="https://bot.derivlite.com">Trading Platform</a></li>
                        <li><a href="#digitsnap">DigitSnap Tool</a></li>
                        <li><a href="#tools">Trading Tools</a></li>
                        <li><a href="#features">Features</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Documentation</a></li>
                        <li><a href="#">Contact Support</a></li>
                        <li><a href="#">Community</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Legal</h4>
                    <ul>
                        <li><a href="#">Terms of Service</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Risk Disclosure</a></li>
                        <li><a href="#">Compliance</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Derivlite. All rights reserved. Trading involves risk and may not be suitable for all investors.</p>
            </div>
        </div>
    </footer>


    <!-- USDT Payment Modal -->

    <!-- USDT Payment Modal -->
    <div id="usdtModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeUSDTModal()">&times;</span>
            <div class="modal-header">
                <i class="fab fa-bitcoin" style="color: #f0b90b; font-size: 24px; margin-right: 10px;"></i>
                <h2>USDT Payment (Binance)</h2>
            </div>

            <div class="usdt-payment-info">
                <div class="payment-details">
                    <h3>Payment Amount: $250 USD</h3>
                    <p class="network-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Payment via Binance Pay Link</strong>
                    </p>
                </div>

                <div class="binance-pay-link-section">
                    <h4>Easy Payment with Binance:</h4>
                    <div class="payment-link-container">
                        <div class="payment-request-box">
                            <div class="request-header">
                                <i class="fas fa-coins" style="color: #f0b90b;"></i>
                                <strong>Payment Request</strong>
                            </div>
                            <p>Derivlite has requested 250 USDT payment. Tap this link to pay.</p>
                            <a href="https://s.binance.com/cOLwTZjQ" target="_blank" class="binance-pay-link" onclick="trackPaymentClick()">
                                <i class="fas fa-external-link-alt"></i>
                                https://s.binance.com/cOLwTZjQ
                            </a>
                        </div>
                    </div>
                </div>

                <div class="payment-steps">
                    <h4>Payment Instructions:</h4>
                    <ol>
                        <li>Click the Binance payment link above</li>
                        <li>Login to your Binance account if prompted</li>
                        <li>Confirm the $250 USDT payment</li>
                        <li>Wait for confirmation (usually instant)</li>
                        <li>Your account will be activated automatically</li>
                    </ol>
                </div>

                <div class="alternative-payment">
                    <h4>Alternative Payment Method:</h4>
                    <div class="wallet-address-section">
                        <label for="walletAddress">Or send USDT directly to this address:</label>
                        <div class="address-container">
                            <input type="text" id="walletAddress" readonly value="******************************************">
                            <button class="copy-btn" onclick="copyWalletAddress()">
                                <i class="fas fa-copy"></i>
                                Copy
                            </button>
                        </div>
                        <p class="address-note">
                            <i class="fas fa-exclamation-triangle"></i> Only send USDT on Binance Smart Chain (BEP20) network.
                        </p>
                    </div>
                </div>

                <div class="security-info">
                    <h4><i class="fas fa-shield-alt"></i> Security Features:</h4>
                    <ul>
                        <li><i class="fas fa-check"></i> Official Binance Pay integration</li>
                        <li><i class="fas fa-check"></i> Secure payment processing</li>
                        <li><i class="fas fa-check"></i> Automatic payment verification</li>
                        <li><i class="fas fa-check"></i> 24/7 transaction monitoring</li>
                    </ul>
                </div>

                <div class="modal-actions">
                    <button class="btn btn-secondary" onclick="closeUSDTModal()">
                        Cancel
                    </button>
                    <button class="btn btn-primary" onclick="confirmUSDTPayment()">
                        <i class="fas fa-check"></i>
                        I've Sent the Payment
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Customer Support Widget -->
    <div class="floating-support">
        <div class="support-chat" id="supportChat">
            <div class="chat-header">
                <h4><i class="fas fa-headset"></i> Customer Support</h4>
                <button class="chat-close" onclick="toggleSupportChat()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="chat-body">
                <div class="chat-message support-message">
                    <div class="message-avatar">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="message-content">
                        <div class="message-text">Hi! I'm here to help you with Derivlite. How can I assist you today?</div>
                        <div class="message-time">Now</div>
                    </div>
                </div>
            </div>
            <div class="chat-footer">
                <div class="quick-actions">
                    <button class="quick-btn" onclick="sendQuickMessage('How do I get started?')">
                        <i class="fas fa-play"></i> Get Started
                    </button>
                    <button class="quick-btn" onclick="sendQuickMessage('Tell me about DigitSnap')">
                        <i class="fas fa-chart-line"></i> About DigitSnap
                    </button>
                    <button class="quick-btn" onclick="sendQuickMessage('Pricing information')">
                        <i class="fas fa-dollar-sign"></i> Pricing
                    </button>
                    <button class="quick-btn contact-btn" onclick="showContactInfo()">
                        <i class="fas fa-phone"></i> Contact Us
                    </button>
                </div>
                <div class="chat-input-area">
                    <input type="text" id="chatInput" placeholder="Type your message..." />
                    <button onclick="sendMessage()">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
        <button class="support-toggle" onclick="toggleSupportChat()">
            <i class="fas fa-comments"></i>
            <span class="support-badge">1</span>
        </button>
    </div>

    <!-- Embedded CSS for Support Widget (fallback) -->
    <style>
        .floating-support {
            position: fixed !important;
            bottom: 20px !important;
            left: 20px !important;
            z-index: 9999 !important;
            font-family: 'Inter', sans-serif !important;
        }

        .support-toggle {
            width: 60px !important;
            height: 60px !important;
            border-radius: 50% !important;
            background: linear-gradient(135deg, #FFD700, #FFC107) !important;
            border: none !important;
            cursor: pointer !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 24px !important;
            color: #000 !important;
            box-shadow: 0 4px 20px rgba(255, 215, 0, 0.4) !important;
            transition: all 0.3s ease !important;
            position: relative !important;
        }

        .support-toggle:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 25px rgba(255, 215, 0, 0.6) !important;
        }

        .support-badge {
            position: absolute !important;
            top: -5px !important;
            right: -5px !important;
            background: #ff4444 !important;
            color: white !important;
            border-radius: 50% !important;
            width: 20px !important;
            height: 20px !important;
            font-size: 12px !important;
            font-weight: bold !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            animation: pulse 2s infinite !important;
        }

        .support-chat {
            position: absolute !important;
            bottom: 80px !important;
            left: 0 !important;
            width: 350px !important;
            height: 450px !important;
            background: #1A1A1A !important;
            border-radius: 15px !important;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3) !important;
            border: 1px solid #2D2D2D !important;
            display: none !important;
            flex-direction: column !important;
            overflow: hidden !important;
        }

        .support-chat.active {
            display: flex !important;
            animation: slideUp 0.3s ease !important;
        }

        .chat-header {
            background: linear-gradient(135deg, #FFD700, #FFC107) !important;
            color: #000 !important;
            padding: 15px 20px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: space-between !important;
            font-weight: 600 !important;
        }

        .chat-header h4 {
            margin: 0 !important;
            font-size: 16px !important;
        }

        .chat-close {
            background: none !important;
            border: none !important;
            color: #000 !important;
            font-size: 18px !important;
            cursor: pointer !important;
            padding: 5px !important;
            border-radius: 50% !important;
        }

        .chat-body {
            flex: 1 !important;
            padding: 20px !important;
            overflow-y: auto !important;
            background: #0D0D0D !important;
        }

        .chat-message {
            display: flex !important;
            margin-bottom: 15px !important;
            align-items: flex-start !important;
        }

        .message-avatar {
            width: 35px !important;
            height: 35px !important;
            border-radius: 50% !important;
            background: #FFD700 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            margin-right: 12px !important;
            flex-shrink: 0 !important;
        }

        .message-avatar i {
            color: #000 !important;
            font-size: 16px !important;
        }

        .message-content {
            background: #2D2D2D !important;
            padding: 12px 15px !important;
            border-radius: 15px 15px 15px 5px !important;
            color: #fff !important;
            font-size: 14px !important;
            line-height: 1.4 !important;
            max-width: 250px !important;
        }

        .message-time {
            font-size: 11px !important;
            color: #999 !important;
            margin-top: 5px !important;
            text-align: right !important;
        }

        .chat-footer {
            padding: 15px 20px !important;
            background: #1A1A1A !important;
            border-top: 1px solid #2D2D2D !important;
        }

        .quick-actions {
            display: flex !important;
            flex-wrap: wrap !important;
            gap: 8px !important;
            margin-bottom: 15px !important;
        }

        .quick-btn {
            background: #2D2D2D !important;
            color: #fff !important;
            border: 1px solid #333 !important;
            padding: 8px 12px !important;
            border-radius: 20px !important;
            font-size: 12px !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
        }

        .quick-btn:hover {
            background: #FFD700 !important;
            color: #000 !important;
        }

        .contact-btn {
            background: linear-gradient(135deg, #FFD700, #FFC107) !important;
            color: #000 !important;
        }

        .chat-input-area {
            display: flex !important;
            gap: 10px !important;
            align-items: center !important;
        }

        .chat-input-area input {
            flex: 1 !important;
            background: #2D2D2D !important;
            border: 1px solid #333 !important;
            color: #fff !important;
            padding: 12px 15px !important;
            border-radius: 25px !important;
            font-size: 14px !important;
            outline: none !important;
        }

        .chat-input-area button {
            width: 40px !important;
            height: 40px !important;
            border-radius: 50% !important;
            background: #FFD700 !important;
            border: none !important;
            color: #000 !important;
            cursor: pointer !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 768px) {
            .support-chat {
                width: calc(100vw - 40px) !important;
                max-width: 320px !important;
            }
        }
    </style>

    <!-- Embedded JavaScript for Support Widget (fallback) -->
    <script>
        // Support Widget Variables
        let isSupportChatOpen = false;

        // Support Widget Functions
        function toggleSupportChat() {
            console.log('toggleSupportChat called');
            const supportChat = document.getElementById('supportChat');
            const supportBadge = document.querySelector('.support-badge');

            if (!supportChat) {
                console.error('supportChat element not found');
                return;
            }

            if (isSupportChatOpen) {
                supportChat.classList.remove('active');
                isSupportChatOpen = false;
                console.log('Chat closed');
            } else {
                supportChat.classList.add('active');
                isSupportChatOpen = true;
                console.log('Chat opened');
                if (supportBadge) {
                    supportBadge.style.display = 'none';
                }
            }
        }

        function sendQuickMessage(message) {
            addMessageToChat(message, true);

            setTimeout(() => {
                let response = "";
                switch (message) {
                    case 'How do I get started?':
                        response = "Hey! So excited you want to jump in! 😊 It's actually super simple - just hit that 'Get Started' button, fill out the quick signup (literally takes like 2 minutes), then you can deposit as little as $50 to begin. Once that's done, you'll have full access to DigitSnap and can start trading right away! I can walk you through it if you want?";
                        break;
                    case 'Tell me about DigitSnap':
                        response = "Oh man, DigitSnap is seriously incredible! 🚀 We're hitting 95% accuracy consistently - I know that sounds too good to be true but our users are making real money with it daily. It basically analyzes the market in real-time and gives you super clear signals on when to enter trades. Works whether you're completely new to this or you've been trading for years. Want me to show you how it works?";
                        break;
                    case 'Pricing information':
                        response = "Sure thing! So we keep it simple - it's just $250 one-time payment for everything. No monthly fees or subscriptions, you pay once and get lifetime access to DigitSnap, all the features, priority support, the works. Pretty amazing deal considering what you get!";
                        break;
                    default:
                        response = "Hey there! What's up? I'm here to help with whatever you need to know about Derivlite! 😊";
                }
                addMessageToChat(response, false);
            }, 1500);
        }

        function sendMessage() {
            const chatInput = document.getElementById('chatInput');
            const message = chatInput.value.trim();

            if (message) {
                addMessageToChat(message, true);
                chatInput.value = '';

                setTimeout(() => {
                    const lowerMessage = message.toLowerCase();
                    let response = "";

                    if (lowerMessage.includes('price') || lowerMessage.includes('cost') || lowerMessage.includes('fee')) {
                        response = "Ah pricing! Super straightforward - it's $250 one-time payment and you're set for life! No monthly subscriptions or recurring charges, just pay once and get full access to DigitSnap, all features, priority support, everything forever. Honestly such a steal for what you get!";
                    } else if (lowerMessage.includes('discount') || lowerMessage.includes('deal') || lowerMessage.includes('special') || lowerMessage.includes('offer')) {
                        response = "Ooh, you're asking about discounts! 😉 We actually do have some special offers running, but I can't share them here in the chat. Hit us up on WhatsApp at <a href='https://wa.me/16134957545' target='_blank' style='color: #FFD700;'>+16134957545</a> and mention you're interested in a discount - the team there can hook you up with something good!";
                    } else if (lowerMessage.includes('contact') || lowerMessage.includes('whatsapp') || lowerMessage.includes('telegram')) {
                        response = `Oh, you want to chat directly? Perfect! 😊 Here's how you can reach us:<br><br>WhatsApp is probably the fastest - just hit me up at <a href="https://wa.me/16134957545" target="_blank" style="color: #FFD700;">+16134957545</a><br><br>Or if you're more of a Telegram person, find us at <a href="https://t.me/binary_blueprint" target="_blank" style="color: #FFD700;">@binary_blueprint</a><br><br>We're online 24/7 and usually get back to people in like 5 minutes or less. Which one works better for you?`;
                    } else {
                        const responses = [
                            "Hmm, tell me more about what you're thinking? I want to make sure I give you the right info!",
                            "Interesting! What specifically about Derivlite caught your attention?",
                            "I'm all ears! What would you like to know?",
                            "Sure thing! What's on your mind?",
                            "Cool! How can I help you out today?",
                            "What's up? Fire away with any questions you've got!"
                        ];
                        response = responses[Math.floor(Math.random() * responses.length)];
                    }

                    addMessageToChat(response, false);
                }, 1000);
            }
        }

        function showContactInfo() {
            const contactMessage = `Hey! So here's how you can get hold of us 😊<br><br>WhatsApp is super quick: <a href="https://wa.me/16134957545" target="_blank" style="color: #FFD700; text-decoration: none;">+16134957545</a> - just click and message us!<br><br>Or Telegram if that's your thing: <a href="https://t.me/binary_blueprint" target="_blank" style="color: #FFD700; text-decoration: none;">@binary_blueprint</a><br><br>Email works too: <a href="mailto:<EMAIL>" style="color: #FFD700; text-decoration: none;"><EMAIL></a><br><br>We're online basically all the time and get back to people crazy fast. What works best for you?`;

            addMessageToChat(contactMessage, false);
        }

        function addMessageToChat(message, isUser) {
            const chatBody = document.querySelector('.chat-body');
            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-message ${isUser ? 'user-message' : 'support-message'}`;

            const now = new Date();
            const time = now.toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
            });

            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <i class="fas ${isUser ? 'fa-user' : 'fa-user-tie'}"></i>
                </div>
                <div class="message-content">
                    <div class="message-text">${message}</div>
                    <div class="message-time">${time}</div>
                </div>
            `;

            chatBody.appendChild(messageDiv);
            chatBody.scrollTop = chatBody.scrollHeight;
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Support widget initialized');

            // Show socials popup when page loads
            setTimeout(function() {
                const popup = document.getElementById('socialsPopup');
                if (popup) {
                    popup.style.display = 'flex';
                    document.body.style.overflow = 'hidden';
                }
            }, 500);

            // Handle Enter key in chat input
            const chatInput = document.getElementById('chatInput');
            if (chatInput) {
                chatInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });
            }

            // Show badge after 3 seconds
            setTimeout(() => {
                const supportBadge = document.querySelector('.support-badge');
                if (supportBadge) {
                    supportBadge.style.display = 'flex';
                }
            }, 3000);
        });

        // Close popup when clicking outside
        document.addEventListener('click', function(e) {
            const popup = document.getElementById('socialsPopup');
            if (popup && e.target === popup) {
                popup.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        });
    </script>

    <script src="script.js"></script>
</body>

</html>