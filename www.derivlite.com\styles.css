/* Reset and Base Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Stylish Scrollbar */

::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: var(--light-black);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, var(--primary-yellow), var(--dark-yellow));
    border-radius: 10px;
    border: 2px solid var(--light-black);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, var(--dark-yellow), #FFB300);
}

/* Firefox Scrollbar */

* {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-yellow) var(--light-black);
}

:root {
    /* Color Scheme - Yellow and Black Theme */
    --primary-yellow: #FFD700;
    --dark-yellow: #FFC107;
    --light-yellow: #FFF9C4;
    --accent-yellow: #FFEB3B;
    --primary-black: #000000;
    --dark-black: #0D0D0D;
    --light-black: #1A1A1A;
    --gray-black: #2D2D2D;
    --soft-black: #333333;
    --white: #FFFFFF;
    --light-gray: #F5F5F5;
    --medium-gray: #999999;
    --dark-gray: #666666;
    --success: #4CAF50;
    --error: #F44336;
    --warning: #FF9800;
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    /* Spacing */
    --container-width: 1200px;
    --section-padding: 80px 0;
    --element-padding: 20px;
    /* Shadows */
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
    --shadow-yellow: 0 4px 20px rgba(255, 215, 0, 0.3);
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--white);
    background-color: var(--primary-black);
    overflow-x: hidden;
}

.container {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */

.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: var(--transition-medium);
}

.nav-container {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
}

.brand-text {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-yellow);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    color: var(--white);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-yellow);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-yellow);
    transition: var(--transition-fast);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-actions {
    display: flex;
    gap: 15px;
    align-items: center;
}

.mobile-nav-actions {
    display: none;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--white);
    transition: var(--transition-fast);
}

/* Button Styles */

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition-fast);
    text-align: center;
}

.btn-primary {
    background: var(--primary-yellow);
    color: var(--primary-black);
}

.btn-primary:hover {
    background: var(--dark-yellow);
    transform: translateY(-2px);
    box-shadow: var(--shadow-yellow);
}

.btn-secondary {
    background: var(--gray-black);
    color: var(--white);
    border: 1px solid var(--medium-gray);
}

.btn-secondary:hover {
    background: var(--soft-black);
    color: var(--primary-yellow);
    border-color: var(--primary-yellow);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
}

.btn-outline {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--primary-yellow);
}

.btn-outline:hover {
    background: var(--primary-yellow);
    color: var(--primary-black);
}

.btn-lg {
    padding: 16px 32px;
    font-size: 16px;
}

.btn-xl {
    padding: 20px 40px;
    font-size: 18px;
}

/* Hero Section */

.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--light-black) 100%);
    overflow: hidden;
}

.hero-container {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 30px 20px;
    width: 100%;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 24px;
}

.highlight {
    color: var(--primary-yellow);
    position: relative;
}

.highlight::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--primary-yellow);
    opacity: 0.3;
}

.hero-description {
    font-size: 1.2rem;
    color: var(--medium-gray);
    margin-bottom: 40px;
    line-height: 1.7;
}

.hero-stats {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-yellow);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--medium-gray);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-actions {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

/* Project X Banner in Hero */

.project-x-banner {
    display: flex;
    align-items: center;
    gap: 15px;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 193, 7, 0.05));
    border: 2px solid var(--primary-yellow);
    border-radius: 12px;
    padding: 16px 20px;
    margin-top: 25px;
    transition: var(--transition-medium);
}

.project-x-banner:hover {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 193, 7, 0.1));
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.2);
}

.banner-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.banner-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.banner-content strong {
    color: var(--primary-yellow);
    font-weight: 700;
    font-size: 1rem;
}

.banner-content span {
    color: var(--medium-gray);
    font-size: 0.85rem;
}

.banner-cta {
    background: var(--primary-yellow);
    color: var(--primary-black);
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition-fast);
    flex-shrink: 0;
}

.banner-cta:hover {
    background: var(--dark-yellow);
    transform: translateY(-1px);
}

/* Trading Dashboard Visual */

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.trading-dashboard {
    background: var(--light-black);
    border-radius: 16px;
    padding: 24px;
    box-shadow: var(--shadow-heavy);
    border: 1px solid var(--gray-black);
    width: 100%;
    max-width: 400px;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.dashboard-title {
    font-weight: 600;
    color: var(--white);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 12px;
    background: var(--success);
    color: var(--white);
    border-radius: 12px;
    font-size: 0.8rem;
}

.status-indicator::before {
    content: '';
    width: 8px;
    height: 8px;
    background: var(--white);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.chart-container {
    background: var(--gray-black);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    height: 150px;
    position: relative;
    overflow: hidden;
}

.chart-placeholder {
    position: relative;
    height: 100%;
    width: 100%;
}

.chart-line {
    position: absolute;
    bottom: 20px;
    left: 10px;
    right: 10px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-yellow), var(--success), var(--primary-yellow));
    border-radius: 2px;
}

.chart-points {
    display: flex;
    justify-content: space-between;
    align-items: end;
    height: 100%;
    padding: 0 10px;
}

.point {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-bottom: 15px;
}

.point.profit {
    background: var(--success);
    box-shadow: 0 0 10px var(--success);
}

.point.loss {
    background: var(--error);
    box-shadow: 0 0 10px var(--error);
}

.accuracy-display {
    display: flex;
    justify-content: center;
}

.accuracy-circle {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100px;
    border: 4px solid var(--primary-yellow);
    border-radius: 50%;
    background: radial-gradient(circle, var(--light-black), var(--gray-black));
}

.accuracy-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-yellow);
}

.accuracy-label {
    font-size: 0.7rem;
    color: var(--medium-gray);
    text-transform: uppercase;
}

/* Hero Background */

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 20% 20%, var(--primary-yellow) 0%, transparent 50%), radial-gradient(circle at 80% 80%, var(--primary-yellow) 0%, transparent 50%);
    opacity: 0.03;
}

/* Section Styles */

section {
    padding: var(--section-padding);
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 16px;
    color: var(--white);
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--medium-gray);
    max-width: 600px;
    margin: 0 auto;
}

/* DigitSnap Section */

.digitsnap {
    background: var(--light-black);
}

.digitsnap-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

/* Pricing Highlight */

.pricing-highlight {
    background: linear-gradient(135deg, var(--gray-black), var(--soft-black));
    padding: 30px;
    border-radius: 16px;
    border: 2px solid var(--primary-yellow);
    margin-bottom: 30px;
    text-align: center;
}

.price-tag {
    margin-bottom: 20px;
}

.price-amount {
    display: block;
    font-size: 3rem;
    font-weight: 800;
    color: var(--primary-yellow);
    line-height: 1;
}

.price-label {
    display: block;
    font-size: 0.9rem;
    color: var(--medium-gray);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 8px;
}

.value-proposition {
    padding-top: 20px;
    border-top: 1px solid var(--gray-black);
}

.roi-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.roi-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--success);
    line-height: 1;
}

.roi-label {
    font-size: 0.8rem;
    color: var(--medium-gray);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 4px;
}

.digitsnap-info h3 {
    font-size: 2rem;
    margin-bottom: 20px;
    color: var(--white);
}

.digitsnap-info p {
    font-size: 1.1rem;
    color: var(--medium-gray);
    margin-bottom: 40px;
    line-height: 1.7;
}

.features-list {
    display: grid;
    gap: 24px;
}

.feature-item {
    display: flex;
    gap: 16px;
    align-items: flex-start;
}

.feature-item i {
    color: var(--primary-yellow);
    font-size: 1.5rem;
    margin-top: 4px;
}

.feature-item h4 {
    color: var(--white);
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.feature-item p {
    color: var(--medium-gray);
    font-size: 0.95rem;
}

/* Demo Container */

.demo-container {
    display: flex;
    justify-content: center;
}

.demo-screen {
    background: var(--gray-black);
    border-radius: 12px;
    padding: 20px;
    width: 100%;
    max-width: 350px;
    border: 1px solid var(--soft-black);
}

.demo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--soft-black);
}

.demo-title {
    font-weight: 600;
    color: var(--primary-yellow);
}

.demo-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.8rem;
    color: var(--success);
}

.status-dot {
    width: 6px;
    height: 6px;
    background: var(--success);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.demo-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 24px;
}

.metric {
    text-align: center;
    padding: 12px;
    background: var(--soft-black);
    border-radius: 8px;
}

.metric-label {
    display: block;
    font-size: 0.7rem;
    color: var(--medium-gray);
    text-transform: uppercase;
    margin-bottom: 4px;
}

.metric-value {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-yellow);
}

.demo-signals {
    display: grid;
    gap: 8px;
}

.signal {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: var(--light-black);
    border-radius: 6px;
    font-size: 0.85rem;
}

.signal.profit {
    border-left: 3px solid var(--success);
}

.signal i {
    color: var(--success);
}

/* Tools Section */

.tools {
    background: var(--primary-black);
}

/* Project X Section */

.project-x {
    background: linear-gradient(135deg, var(--dark-black), var(--light-black));
    border-top: 3px solid var(--primary-yellow);
    border-bottom: 3px solid var(--primary-yellow);
    position: relative;
    overflow: hidden;
}

.project-x::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 50%, rgba(255, 215, 0, 0.05), transparent 50%), radial-gradient(circle at 80% 50%, rgba(255, 215, 0, 0.05), transparent 50%);
    pointer-events: none;
}

.project-x-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.project-x-hero {
    text-align: center;
}

.project-x-badge {
    display: inline-flex;
    flex-direction: column;
    background: linear-gradient(135deg, var(--primary-yellow), var(--dark-yellow));
    color: var(--primary-black);
    padding: 20px 30px;
    border-radius: 16px;
    margin-bottom: 30px;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 2px;
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
    transform: rotate(-2deg);
}

.badge-text {
    font-size: 1.2rem;
    line-height: 1;
}

.badge-limit {
    font-size: 0.9rem;
    margin-top: 4px;
    opacity: 0.8;
}

.project-timeline {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    margin-bottom: 40px;
    padding: 25px;
    background: var(--gray-black);
    border-radius: 12px;
    border: 1px solid var(--primary-yellow);
}

.timeline-item {
    text-align: center;
}

.timeline-date {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-yellow);
    margin-bottom: 8px;
}

.timeline-desc {
    font-size: 0.9rem;
    color: var(--medium-gray);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.timeline-arrow {
    font-size: 1.5rem;
    color: var(--primary-yellow);
    font-weight: bold;
}

.transformation-stats {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--soft-black);
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    border: 2px solid var(--gray-black);
    min-width: 120px;
}

.stat-card.highlighted {
    border-color: var(--primary-yellow);
    background: linear-gradient(135deg, var(--gray-black), var(--soft-black));
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
}

.stat-label {
    font-size: 0.85rem;
    color: var(--medium-gray);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 8px;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--white);
}

.stat-card.highlighted .stat-value {
    color: var(--primary-yellow);
}

.transformation-arrow {
    font-size: 1.5rem;
    color: var(--primary-yellow);
}

.project-x-details h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--white);
}

.emphasis-text {
    font-size: 1.1rem;
    color: var(--primary-yellow);
    font-weight: 600;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 215, 0, 0.1);
    border-left: 4px solid var(--primary-yellow);
    border-radius: 8px;
}

.project-features {
    margin-bottom: 30px;
}

.feature-highlight {
    display: flex;
    gap: 16px;
    align-items: flex-start;
    margin-bottom: 25px;
    padding: 20px;
    background: var(--gray-black);
    border-radius: 12px;
    border: 1px solid rgba(255, 215, 0, 0.2);
    transition: var(--transition-medium);
}

.feature-highlight:hover {
    border-color: var(--primary-yellow);
    transform: translateX(5px);
}

.feature-highlight i {
    color: var(--primary-yellow);
    font-size: 1.5rem;
    margin-top: 4px;
    flex-shrink: 0;
}

.feature-highlight h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--white);
}

.feature-highlight p {
    color: var(--medium-gray);
    line-height: 1.5;
}

.urgency-box {
    background: linear-gradient(135deg, rgba(255, 69, 58, 0.1), rgba(255, 159, 10, 0.1));
    border: 2px solid #FF6B35;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    text-align: center;
}

.urgency-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
    font-weight: 700;
    font-size: 1.1rem;
    color: #FF6B35;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.urgency-box p {
    color: var(--white);
    font-size: 1rem;
    line-height: 1.6;
}

.project-x-cta {
    text-align: center;
}

.project-x-btn {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px 40px;
    font-size: 1.2rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-yellow), var(--dark-yellow));
    color: var(--primary-black);
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
    transition: var(--transition-medium);
    margin-bottom: 15px;
}

.project-x-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 215, 0, 0.6);
    background: linear-gradient(135deg, var(--dark-yellow), #FFB300);
}

.btn-subtitle {
    font-size: 0.8rem;
    font-weight: 500;
    opacity: 0.8;
    text-transform: none;
    letter-spacing: 0.5px;
}

.cta-note {
    color: var(--medium-gray);
    font-size: 0.9rem;
    font-style: italic;
    margin-top: 10px;
}

.btn-xl {
    padding: 20px 40px;
    font-size: 1.2rem;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.tool-card {
    background: var(--light-black);
    padding: 30px;
    border-radius: 16px;
    border: 1px solid var(--gray-black);
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.tool-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-yellow);
}

.tool-card.featured {
    border: 2px solid var(--primary-yellow);
    background: linear-gradient(135deg, var(--light-black), var(--gray-black));
}

.tool-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    background: var(--primary-yellow);
    color: var(--primary-black);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.tool-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-yellow);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.tool-icon i {
    font-size: 1.5rem;
    color: var(--primary-black);
}

.tool-card h3 {
    font-size: 1.3rem;
    margin-bottom: 12px;
    color: var(--white);
}

.tool-card p {
    color: var(--medium-gray);
    margin-bottom: 20px;
    line-height: 1.6;
}

.tool-features {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tool-features span {
    background: var(--gray-black);
    color: var(--primary-yellow);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.tool-cta {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--gray-black);
}

.tool-cta .btn {
    width: 100%;
    justify-content: center;
}

/* Platform Section */

.platform {
    background: var(--light-black);
}

.platform-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.platform-info h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--white);
}

.platform-info p {
    font-size: 1.1rem;
    color: var(--medium-gray);
    margin-bottom: 40px;
    line-height: 1.7;
}

.platform-features {
    display: grid;
    gap: 24px;
    margin-bottom: 40px;
}

.platform-feature {
    display: flex;
    gap: 16px;
    align-items: flex-start;
}

.platform-feature i {
    color: var(--primary-yellow);
    font-size: 1.5rem;
    margin-top: 4px;
}

.platform-feature h4 {
    color: var(--white);
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.platform-feature p {
    color: var(--medium-gray);
    font-size: 0.95rem;
    margin-bottom: 0;
}

/* Platform Preview */

.platform-preview {
    display: flex;
    justify-content: center;
}

.platform-screen {
    background: var(--gray-black);
    border-radius: 16px;
    overflow: hidden;
    width: 100%;
    max-width: 450px;
    box-shadow: var(--shadow-heavy);
}

.screen-header {
    background: var(--soft-black);
    padding: 12px 20px;
    display: flex;
    align-items: center;
    gap: 16px;
}

.screen-controls {
    display: flex;
    gap: 6px;
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.control.red {
    background: #ff5f56;
}

.control.yellow {
    background: #ffbd2e;
}

.control.green {
    background: #27ca3f;
}

.screen-url {
    background: var(--light-black);
    padding: 4px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    color: var(--medium-gray);
}

.screen-content {
    padding: 20px;
}

.platform-dashboard {
    display: flex;
    gap: 16px;
}

.dashboard-sidebar {
    width: 120px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.sidebar-item {
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    color: var(--medium-gray);
    cursor: pointer;
    transition: var(--transition-fast);
}

.sidebar-item.active {
    background: var(--primary-yellow);
    color: var(--primary-black);
}

.dashboard-main {
    flex: 1;
}

.dashboard-cards {
    display: grid;
    gap: 12px;
}

.dashboard-card {
    background: var(--light-black);
    padding: 16px;
    border-radius: 8px;
}

.card-title {
    font-size: 0.8rem;
    color: var(--medium-gray);
    margin-bottom: 4px;
}

.card-value {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--white);
}

.card-value.profit {
    color: var(--success);
}

/* Features Section */

.features {
    background: var(--primary-black);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.feature-card {
    background: var(--light-black);
    padding: 30px;
    border-radius: 16px;
    border: 1px solid var(--gray-black);
    transition: var(--transition-medium);
    position: relative;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-yellow);
}

.feature-number {
    position: absolute;
    top: -15px;
    left: 30px;
    background: var(--primary-yellow);
    color: var(--primary-black);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.1rem;
}

.feature-card h3 {
    font-size: 1.3rem;
    margin: 20px 0 12px 0;
    color: var(--white);
}

.feature-card p {
    color: var(--medium-gray);
    line-height: 1.6;
}

/* Download Section - Using inline styles in HTML for guaranteed compatibility */

/* CTA Section */

.platforms-section {
    text-align: center;
}

.platforms-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--white);
    margin-bottom: 20px;
}

.platforms-container {
    overflow: hidden;
    width: 100%;
    position: relative;
    mask: linear-gradient( to right, transparent 0%, black 15%, black 85%, transparent 100%);
    -webkit-mask: linear-gradient( to right, transparent 0%, black 15%, black 85%, transparent 100%);
}

.platforms-track {
    display: flex;
    gap: 24px;
    animation: scroll-platforms 25s linear infinite;
    width: max-content;
}

.platforms-track:hover {
    animation-play-state: paused;
}

@keyframes scroll-platforms {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

.platform {
    background: rgba(255, 215, 0, 0.08);
    border: 1px solid rgba(255, 215, 0, 0.15);
    border-radius: 12px;
    padding: 16px 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    min-width: 90px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.platform:hover {
    background: rgba(255, 215, 0, 0.15);
    border-color: rgba(255, 215, 0, 0.3);
    transform: translateY(-4px);
}

.platform i {
    font-size: 1.8rem;
    color: var(--primary-yellow);
}

.platform span {
    font-size: 0.85rem;
    color: var(--medium-gray);
    font-weight: 500;
    white-space: nowrap;
}

/* Download Section Responsive */

@media (max-width: 768px) {
    .download-header h2 {
        font-size: 2rem;
    }
    .download-main {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    .download-card {
        padding: 30px 20px;
    }
    .card-icon {
        width: 64px;
        height: 64px;
        border-radius: 16px;
    }
    .card-icon i {
        font-size: 1.5rem;
    }
    .download-stats {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    .stat {
        padding: 20px 16px;
    }
    .stat-value {
        font-size: 1.5rem;
    }
    .platform {
        padding: 12px 16px;
        min-width: 70px;
    }
    .platform i {
        font-size: 1.4rem;
    }
    .platform span {
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    .download-header h2 {
        font-size: 1.8rem;
    }
    .download-header p {
        font-size: 1rem;
    }
    .download-card {
        padding: 24px 16px;
    }
    .card-badge {
        font-size: 0.7rem;
        padding: 6px 12px;
    }
    .btn-large {
        padding: 14px 24px;
        font-size: 1rem;
    }
    .platforms-section h4 {
        font-size: 1rem;
    }
    .platform {
        padding: 10px 12px;
        min-width: 60px;
    }
    .platform i {
        font-size: 1.2rem;
    }
    .platform span {
        font-size: 0.7rem;
    }
}

/* CTA Section */

.cta {
    background: linear-gradient(135deg, var(--light-black), var(--gray-black));
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 16px;
    color: var(--white);
}

.cta-content p {
    font-size: 1.1rem;
    color: var(--medium-gray);
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-pricing {
    margin-bottom: 40px;
}

.cta-price-box {
    background: var(--gray-black);
    border: 2px solid var(--primary-yellow);
    border-radius: 16px;
    padding: 30px;
    max-width: 300px;
    margin: 0 auto;
}

.cta-price {
    font-size: 3.5rem;
    font-weight: 800;
    color: var(--primary-yellow);
    line-height: 1;
    margin-bottom: 8px;
}

.cta-price-label {
    font-size: 1rem;
    color: var(--medium-gray);
    margin-bottom: 16px;
}

.cta-guarantee {
    font-size: 0.9rem;
    color: var(--success);
    background: rgba(76, 175, 80, 0.1);
    padding: 8px 16px;
    border-radius: 20px;
    border: 1px solid var(--success);
}

.cta-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
}

.cta-info {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
    justify-content: center;
}

.cta-point {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--medium-gray);
}

.cta-point i {
    color: var(--primary-yellow);
}

/* Footer */

.footer {
    background: var(--dark-black);
    padding: 60px 0 20px 0;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.footer-logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
}

.footer-brand-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-yellow);
}

.footer-section p {
    color: var(--medium-gray);
    margin-bottom: 20px;
    line-height: 1.6;
}

.footer-section h4 {
    color: var(--white);
    margin-bottom: 16px;
    font-size: 1.1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: var(--medium-gray);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--primary-yellow);
}

.social-links {
    display: flex;
    gap: 12px;
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--gray-black);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition-fast);
}

.social-link:hover {
    background: var(--primary-yellow);
    color: var(--primary-black);
}

.footer-bottom {
    padding-top: 20px;
    border-top: 1px solid var(--gray-black);
    text-align: center;
    color: var(--dark-gray);
    font-size: 0.9rem;
}

/* Payment Methods */

.payment-methods {
    margin: 20px 0;
    text-align: center;
}

.payment-methods h4 {
    color: #f0b90b;
    margin-bottom: 15px;
    font-size: 14px;
    font-weight: 600;
}

.payment-options {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-bottom: 20px;
}

.payment-option {
    flex: 1;
    padding: 12px 8px;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    color: #ccc;
    font-size: 12px;
}

.payment-option i {
    font-size: 18px;
    color: #f0b90b;
}

.payment-option:hover {
    background: rgba(240, 185, 11, 0.1);
    border-color: rgba(240, 185, 11, 0.3);
    color: #f0b90b;
}

.payment-option.active {
    background: rgba(240, 185, 11, 0.2);
    border-color: #f0b90b;
    color: #f0b90b;
    box-shadow: 0 0 10px rgba(240, 185, 11, 0.2);
}

.payment-option.active i {
    color: #fdd835;
}

/* Binance Modal Styles */

.binance-payment-info {
    padding: 20px 0;
}

.binance-pay-section {
    margin-bottom: 25px;
}

.binance-pay-section h4 {
    color: #f0b90b;
    margin-bottom: 15px;
    font-size: 16px;
}

.binance-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.binance-option {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #fff;
    text-align: left;
}

.binance-option:hover {
    background: rgba(240, 185, 11, 0.1);
    border-color: rgba(240, 185, 11, 0.3);
    transform: translateY(-2px);
}

.binance-option i {
    font-size: 24px;
    color: #f0b90b;
    min-width: 30px;
}

.binance-option div {
    flex: 1;
}

.binance-option strong {
    display: block;
    color: #f0b90b;
    font-size: 16px;
    margin-bottom: 5px;
}

.binance-option p {
    margin: 0;
    color: #ccc;
    font-size: 14px;
}

/* USDT Modal Styles */

.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--light-black);
    border: 1px solid var(--gray-black);
    border-radius: 16px;
    width: 90%;
    max-width: 450px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-heavy);
    animation: slideInUp 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 30px;
    border-bottom: 1px solid var(--gray-black);
}

.modal-header h2 {
    color: var(--white);
    font-size: 1.5rem;
    margin: 0;
}

.modal-close {
    color: var(--medium-gray);
    font-size: 24px;
    cursor: pointer;
    transition: var(--transition-fast);
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.modal-close:hover {
    color: var(--primary-yellow);
    background: var(--gray-black);
}

.modal-body {
    padding: 30px;
}

/* Auth Tabs */

.auth-tabs {
    display: flex;
    margin-bottom: 30px;
    background: var(--gray-black);
    border-radius: 8px;
    padding: 4px;
}

.auth-tab {
    flex: 1;
    padding: 12px 20px;
    background: transparent;
    border: none;
    color: var(--medium-gray);
    cursor: pointer;
    border-radius: 6px;
    transition: var(--transition-fast);
    font-weight: 500;
}

.auth-tab.active {
    background: var(--primary-yellow);
    color: var(--primary-black);
}

/* Auth Forms */

.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-group label {
    display: block;
    color: var(--white);
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 0.9rem;
}

.form-group input {
    width: 100%;
    padding: 14px 16px 14px 45px;
    background: var(--gray-black);
    border: 1px solid var(--soft-black);
    border-radius: 8px;
    color: var(--white);
    font-size: 1rem;
    transition: var(--transition-fast);
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-yellow);
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.form-group input::placeholder {
    color: var(--dark-gray);
}

.form-icon {
    position: absolute;
    left: 16px;
    top: 38px;
    color: var(--medium-gray);
    font-size: 0.9rem;
}

.password-toggle {
    position: absolute;
    right: 16px;
    top: 38px;
    background: none;
    border: none;
    color: var(--medium-gray);
    cursor: pointer;
    font-size: 0.9rem;
    transition: var(--transition-fast);
}

.password-toggle:hover {
    color: var(--primary-yellow);
}

/* Form Options */

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 10px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: var(--medium-gray);
    font-size: 0.9rem;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    background: var(--gray-black);
    border: 1px solid var(--soft-black);
    border-radius: 4px;
    margin-right: 8px;
    position: relative;
    transition: var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked+.checkmark {
    background: var(--primary-yellow);
    border-color: var(--primary-yellow);
}

.checkbox-label input[type="checkbox"]:checked+.checkmark::after {
    content: '\2714';
    position: absolute;
    color: var(--primary-black);
    font-size: 12px;
    font-weight: bold;
    left: 3px;
    top: 1px;
}

.forgot-password,
.terms-link {
    color: var(--primary-yellow);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition-fast);
}

.forgot-password:hover,
.terms-link:hover {
    color: var(--dark-yellow);
}

/* Password Strength */

.password-strength {
    margin-bottom: 20px;
}

.strength-meter {
    width: 100%;
    height: 4px;
    background: var(--gray-black);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 8px;
}

.strength-bar {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-text {
    font-size: 0.8rem;
    color: var(--medium-gray);
}

/* Button Styles for Forms */

.btn-full {
    width: 100%;
    padding: 16px;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 20px;
}

/* Animations */

@keyframes pulse {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Scroll animations */

.hero-text {
    animation: slideInLeft 0.8s ease-out;
}

.hero-visual {
    animation: slideInRight 0.8s ease-out 0.2s both;
}

/* Responsive Design */

@media (max-width: 1024px) {
    :root {
        --container-width: 100%;
    }
    .hero-content,
    .digitsnap-content,
    .project-x-content,
    .platform-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    .hero-visual,
    .platform-preview {
        order: -1;
    }
    .hero-title {
        font-size: 3rem;
    }
    .tools-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(0, 0, 0, 0.98);
        backdrop-filter: blur(10px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 50px;
        transition: left 0.3s ease;
        z-index: 999;
        display: flex;
    }
    .nav-menu.active {
        left: 0;
    }
    .nav-menu li {
        margin: 20px 0;
        width: 100%;
        text-align: center;
    }
    .nav-link {
        font-size: 1.2rem;
        padding: 15px 20px;
        display: block;
        width: 100%;
        border-bottom: 1px solid var(--gray-black);
        transition: var(--transition-fast);
    }
    .nav-link:hover {
        background: var(--gray-black);
        color: var(--primary-yellow);
    }
    .nav-actions {
        display: none;
    }
    .mobile-nav-actions {
        display: flex;
        flex-direction: column;
        gap: 15px;
        margin-top: 30px;
        width: 80%;
    }
    .mobile-nav-actions .btn {
        width: 100%;
        justify-content: center;
        padding: 15px 20px;
    }
    .hamburger {
        display: flex;
    }
    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }
    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    /* Hide trading dashboard on small devices */
    .hero-visual,
    .trading-dashboard {
        display: none;
    }
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    .hero-title {
        font-size: 2.5rem;
    }
    .hero-stats {
        gap: 20px;
    }
    .hero-actions {
        flex-direction: column;
        align-items: stretch;
    }
    .project-x-banner {
        flex-direction: column;
        text-align: center;
        gap: 12px;
        padding: 14px 16px;
    }
    .banner-content {
        gap: 6px;
    }
    .section-title {
        font-size: 2rem;
    }
    .pricing-highlight {
        margin-bottom: 20px;
        padding: 20px;
    }
    .price-amount {
        font-size: 2.5rem;
    }
    .cta-price {
        font-size: 3rem;
    }
    .cta-price-box {
        padding: 20px;
    }
    .cta-info {
        flex-direction: column;
        gap: 16px;
    }
    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    .hero-title {
        font-size: 2rem;
    }
    .hero-description {
        font-size: 1rem;
    }
    .hero-stats {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    .stat-number {
        font-size: 2rem;
    }
    .tools-grid,
    .features-grid {
        grid-template-columns: 1fr;
    }
    .tool-card,
    .feature-card {
        padding: 20px;
    }
    /* Project X Mobile Styles */
    .project-x-badge {
        padding: 15px 20px;
        transform: rotate(0deg);
    }
    .badge-text {
        font-size: 1rem;
    }
    .transformation-stats {
        flex-direction: column;
        gap: 15px;
    }
    .timeline-arrow {
        transform: rotate(90deg);
        margin: 10px 0;
    }
    .project-timeline {
        flex-direction: column;
        gap: 15px;
    }
    .feature-highlight {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }
    .project-x-btn {
        padding: 18px 30px;
        font-size: 1.1rem;
    }
    .urgency-header {
        flex-direction: column;
        gap: 8px;
    }
    section {
        padding: 60px 0;
    }
    /* Modal Mobile Styles */
    .modal-content {
        width: 95%;
        max-height: 95vh;
    }
    .modal-header,
    .modal-body {
        padding: 20px;
    }
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
}

/* Authentication Modal Styles */

.auth-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 10000;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(5px);
}

.auth-modal-content {
    background: var(--light-black);
    border-radius: 15px;
    padding: 40px;
    width: 90%;
    max-width: 450px;
    box-shadow: var(--shadow-heavy);
    border: 1px solid var(--gray-black);
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header h2 {
    color: var(--primary-yellow);
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 10px;
}

.auth-header p {
    color: var(--medium-gray);
    font-size: 16px;
}

.auth-form {
    display: block;
}

.auth-form .form-group {
    margin-bottom: 20px;
    position: relative;
}

.auth-form label {
    display: block;
    color: var(--white);
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 14px;
}

.auth-form input {
    width: 100%;
    padding: 15px 45px 15px 15px;
    background: var(--gray-black);
    border: 1px solid var(--soft-black);
    border-radius: 8px;
    color: var(--white);
    font-size: 16px;
    transition: var(--transition-medium);
}

.auth-form input:focus {
    outline: none;
    border-color: var(--primary-yellow);
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.2);
}

.auth-form .form-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--medium-gray);
    pointer-events: none;
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--medium-gray);
    cursor: pointer;
    padding: 5px;
    transition: var(--transition-fast);
}

.password-toggle:hover {
    color: var(--primary-yellow);
}

.auth-switch {
    text-align: center;
    margin-top: 20px;
    color: var(--medium-gray);
}

.auth-switch a {
    color: var(--primary-yellow);
    text-decoration: none;
    font-weight: 500;
}

.auth-switch a:hover {
    text-decoration: underline;
}

/* Payment Modal Styles */

.payment-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    z-index: 10001;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(10px);
    overflow-y: auto;
    padding: 20px;
}

.payment-modal-content {
    background: var(--light-black);
    border-radius: 20px;
    padding: 40px;
    width: 100%;
    max-width: 800px;
    box-shadow: var(--shadow-heavy);
    border: 1px solid var(--gray-black);
    position: relative;
    animation: modalSlideIn 0.4s ease;
    max-height: 90vh;
    overflow-y: auto;
}

.payment-header {
    text-align: center;
    margin-bottom: 40px;
}

.payment-icon {
    font-size: 48px;
    color: var(--primary-yellow);
    margin-bottom: 20px;
}

.payment-header h2 {
    color: var(--white);
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 15px;
}

.payment-header p {
    color: var(--medium-gray);
    font-size: 18px;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

.payment-plans {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 40px;
}

.plan-card {
    background: var(--gray-black);
    border: 2px solid var(--soft-black);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    position: relative;
    transition: var(--transition-medium);
    max-width: 400px;
    width: 100%;
}

.plan-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-yellow);
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.2);
}

.plan-card.featured {
    border-color: var(--primary-yellow);
    background: linear-gradient(135deg, var(--gray-black) 0%, rgba(255, 215, 0, 0.1) 100%);
}

.plan-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-yellow);
    color: var(--primary-black);
    padding: 5px 20px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
}

.plan-card h3 {
    color: var(--white);
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
}

.plan-price {
    margin-bottom: 30px;
}

.plan-price .currency {
    color: var(--primary-yellow);
    font-size: 24px;
    font-weight: 600;
    vertical-align: top;
}

.plan-price .amount {
    color: var(--white);
    font-size: 48px;
    font-weight: 700;
}

.plan-price .period {
    color: var(--medium-gray);
    font-size: 18px;
}

.plan-features {
    list-style: none;
    margin-bottom: 30px;
}

.plan-features li {
    padding: 10px 0;
    color: var(--white);
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.plan-features li i {
    margin-right: 12px;
    width: 16px;
}

.plan-features li .fa-check {
    color: var(--success);
}

.plan-features li .fa-times {
    color: var(--medium-gray);
}

.payment-methods {
    margin: 30px 0;
}

.payment-methods h4 {
    color: var(--white);
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    text-align: center;
}

.payment-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 20px;
}

.payment-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px 10px;
    background: var(--soft-black);
    border: 2px solid var(--gray-black);
    border-radius: 10px;
    color: var(--white);
    cursor: pointer;
    transition: var(--transition-medium);
    font-size: 14px;
}

.payment-option:hover {
    border-color: var(--primary-yellow);
    background: var(--gray-black);
}

.payment-option.active {
    border-color: var(--primary-yellow);
    background: rgba(255, 215, 0, 0.1);
    color: var(--primary-yellow);
}

.payment-option i {
    font-size: 20px;
}

.payment-footer {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid var(--soft-black);
}

.payment-note {
    color: var(--medium-gray);
    font-size: 14px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.payment-note i {
    color: var(--success);
}

.payment-terms {
    color: var(--medium-gray);
    font-size: 12px;
}

.payment-terms a {
    color: var(--primary-yellow);
    text-decoration: none;
}

.payment-terms a:hover {
    text-decoration: underline;
}

/* Responsive Payment Modal */

@media (max-width: 768px) {
    .payment-modal {
        padding: 10px;
    }
    .payment-modal-content {
        padding: 30px 20px;
        max-height: 95vh;
    }
    .payment-plans {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    .payment-header h2 {
        font-size: 24px;
    }
    .payment-header p {
        font-size: 16px;
    }
    .plan-price .amount {
        font-size: 36px;
    }
}

/* Enhanced scrollbar for modal content areas */

.auth-modal-content::-webkit-scrollbar,
.payment-modal-content::-webkit-scrollbar {
    width: 8px;
}

.auth-modal-content::-webkit-scrollbar-track,
.payment-modal-content::-webkit-scrollbar-track {
    background: rgba(26, 26, 26, 0.5);
    border-radius: 8px;
}

.auth-modal-content::-webkit-scrollbar-thumb,
.payment-modal-content::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, var(--primary-yellow), var(--dark-yellow));
    border-radius: 8px;
    border: 1px solid rgba(26, 26, 26, 0.8);
}

.auth-modal-content::-webkit-scrollbar-thumb:hover,
.payment-modal-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, var(--dark-yellow), #FFB300);
}

/* Binance Payment Link Styling */

.binance-pay-link-section {
    margin: 20px 0;
}

.payment-link-container {
    margin: 15px 0;
}

.payment-request-box {
    background: linear-gradient(145deg, #1a1a1a, #000000);
    border: 2px solid var(--primary-yellow);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
}

.request-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
    color: var(--primary-yellow);
    font-size: 18px;
    font-weight: bold;
}

.payment-request-box p {
    color: var(--light-gray);
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 1.5;
}

.binance-pay-link {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(145deg, var(--primary-yellow), var(--dark-yellow));
    color: var(--primary-black);
    text-decoration: none;
    padding: 15px 25px;
    border-radius: 8px;
    font-weight: bold;
    font-size: 16px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    word-break: break-all;
}

.binance-pay-link:hover {
    background: linear-gradient(145deg, var(--dark-yellow), #FFB300);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
    border-color: var(--accent-yellow);
}

.binance-pay-link:active {
    transform: translateY(0);
}

.alternative-payment {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--gray-black);
}

.alternative-payment h4 {
    color: var(--medium-gray);
    font-size: 16px;
    margin-bottom: 15px;
}

.alternative-payment .wallet-address-section {
    margin-top: 15px;
}

.alternative-payment .address-container input {
    background: rgba(26, 26, 26, 0.8);
    border: 1px solid var(--gray-black);
}

.alternative-payment .address-note {
    font-size: 14px;
    color: var(--medium-gray);
}

/* Floating Customer Support Widget */

.floating-support {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 1000;
    font-family: 'Inter', sans-serif;
}

.support-toggle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-yellow), var(--dark-yellow));
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: var(--primary-black);
    box-shadow: 0 4px 20px rgba(255, 215, 0, 0.4);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.support-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(255, 215, 0, 0.6);
}

.support-toggle:active {
    transform: translateY(0);
}

.support-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4444;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.support-chat {
    position: absolute;
    bottom: 80px;
    left: 0;
    width: 350px;
    height: 450px;
    background: var(--light-black);
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--gray-black);
    display: none;
    flex-direction: column;
    overflow: hidden;
    animation: slideUp 0.3s ease;
}

.support-chat.active {
    display: flex;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.chat-header {
    background: linear-gradient(135deg, var(--primary-yellow), var(--dark-yellow));
    color: var(--primary-black);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
}

.chat-header h4 {
    margin: 0;
    font-size: 16px;
}

.chat-header i {
    margin-right: 8px;
}

.chat-close {
    background: none;
    border: none;
    color: var(--primary-black);
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.2s ease;
}

.chat-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

.chat-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: var(--dark-black);
}

.chat-message {
    display: flex;
    margin-bottom: 15px;
    align-items: flex-start;
    width: 100%;
    justify-content: flex-start;
}

.message-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: var(--primary-yellow);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
}

.message-avatar i {
    color: var(--primary-black);
    font-size: 16px;
}

.message-content {
    background: var(--gray-black);
    padding: 12px 15px;
    border-radius: 15px 15px 15px 5px;
    color: var(--white);
    font-size: 14px;
    line-height: 1.4;
    max-width: 250px;
    position: relative;
}

.message-text {
    margin-bottom: 5px;
}

.message-time {
    font-size: 11px;
    color: var(--medium-gray);
    text-align: right;
    margin-top: 5px;
    opacity: 0.7;
}

.message-content a {
    color: var(--primary-yellow);
    text-decoration: underline;
}

.message-content a:hover {
    color: var(--dark-yellow);
}

.user-message {
    flex-direction: row-reverse !important;
    justify-content: flex-start !important;
}

.user-message .message-avatar {
    background: var(--dark-yellow) !important;
    margin-right: 0 !important;
    margin-left: 12px !important;
}

.user-message .message-content {
    background: var(--primary-yellow) !important;
    color: var(--primary-black) !important;
    border-radius: 15px 15px 5px 15px !important;
    margin-left: auto !important;
}

/* Support messages - left aligned, gray background */

.support-message {
    flex-direction: row !important;
    justify-content: flex-start !important;
}

.support-message .message-avatar {
    background: var(--primary-yellow) !important;
    margin-right: 12px !important;
    margin-left: 0 !important;
}

.support-message .message-content {
    background: var(--gray-black) !important;
    color: var(--white) !important;
    border-radius: 15px 15px 15px 5px !important;
    margin-right: auto !important;
}

.chat-footer {
    padding: 15px 20px;
    background: var(--light-black);
    border-top: 1px solid var(--gray-black);
}

.quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.quick-btn {
    background: var(--gray-black);
    color: var(--white);
    border: 1px solid var(--soft-black);
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.quick-btn:hover {
    background: var(--primary-yellow);
    color: var(--primary-black);
    border-color: var(--primary-yellow);
}

.quick-btn i {
    font-size: 11px;
}

.contact-btn {
    background: linear-gradient(135deg, var(--primary-yellow), var(--dark-yellow)) !important;
    color: var(--primary-black) !important;
    border-color: var(--primary-yellow) !important;
    font-weight: 600;
}

.contact-btn:hover {
    background: linear-gradient(135deg, var(--dark-yellow), #FFB300) !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

/* Typing Indicator */

.typing-indicator .message-content {
    padding: 12px 15px;
    background: var(--gray-black);
}

.typing-dots {
    display: flex;
    gap: 4px;
    align-items: center;
    height: 20px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--medium-gray);
    animation: typing 1.5s infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%,
    60%,
    100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

.chat-input-area {
    display: flex;
    gap: 10px;
    align-items: center;
}

.chat-input-area input {
    flex: 1;
    background: var(--gray-black);
    border: 1px solid var(--soft-black);
    color: var(--white);
    padding: 12px 15px;
    border-radius: 25px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s ease;
}

.chat-input-area input:focus {
    border-color: var(--primary-yellow);
}

.chat-input-area input::placeholder {
    color: var(--medium-gray);
}

.chat-input-area button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-yellow);
    border: none;
    color: var(--primary-black);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.chat-input-area button:hover {
    background: var(--dark-yellow);
    transform: scale(1.05);
}

/* Mobile Responsiveness for Support Widget */

@media (max-width: 768px) {
    .floating-support {
        bottom: 15px;
        left: 15px;
    }
    .support-chat {
        width: calc(100vw - 30px);
        max-width: 320px;
        height: 400px;
    }
    .support-toggle {
        width: 55px;
        height: 55px;
        font-size: 22px;
    }
    .quick-actions {
        flex-direction: column;
    }
    .quick-btn {
        width: 100%;
        justify-content: center;
    }
}

/* Social Media Popup Styles */

.socials-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.popup-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.popup-content {
    position: relative;
    background: var(--light-black);
    border: 2px solid var(--primary-yellow);
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    animation: popupSlideIn 0.3s ease-out;
}

@keyframes popupSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.popup-header {
    background: linear-gradient(135deg, var(--primary-yellow), var(--dark-yellow));
    color: var(--primary-black);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.popup-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.popup-close {
    background: none;
    border: none;
    color: var(--primary-black);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.popup-close:hover {
    background: rgba(0, 0, 0, 0.1);
    transform: rotate(90deg);
}

.popup-body {
    padding: 30px;
    color: var(--white);
}

.popup-body p {
    text-align: center;
    margin-bottom: 25px;
    font-size: 1.1rem;
    color: var(--light-gray);
}

.popup-body .social-links {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 30px;
}

.popup-body .social-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px 20px;
    background: var(--gray-black);
    border: 2px solid transparent;
    border-radius: 10px;
    color: var(--white);
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
    width: 100%;
    justify-content: flex-start;
}

.popup-body .social-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.2);
}

.popup-body .social-link.whatsapp {
    border-color: #25D366;
}

.popup-body .social-link.whatsapp:hover {
    background: linear-gradient(135deg, #25D366, #128C7E);
    border-color: #25D366;
}

.popup-body .social-link.telegram {
    border-color: #0088CC;
}

.popup-body .social-link.telegram:hover {
    background: linear-gradient(135deg, #0088CC, #005577);
    border-color: #0088CC;
}

.popup-body .social-link.instagram {
    border-color: #E4405F;
}

.popup-body .social-link.instagram:hover {
    background: linear-gradient(135deg, #E4405F, #C13584, #833AB4);
    border-color: #E4405F;
}

.popup-body .social-link.email {
    border-color: var(--primary-yellow);
}

.popup-body .social-link.email:hover {
    background: linear-gradient(135deg, var(--primary-yellow), var(--dark-yellow));
    color: var(--primary-black);
    border-color: var(--primary-yellow);
}

.popup-body .social-link i {
    font-size: 1.5rem;
    width: 24px;
    text-align: center;
    flex-shrink: 0;
}

.popup-body .social-link span {
    font-size: 1rem;
    white-space: nowrap;
}

.popup-footer {
    text-align: center;
    margin-top: 20px;
}

.popup-footer .btn {
    padding: 12px 30px;
    font-size: 1rem;
    border-radius: 8px;
}

/* Mobile responsive */

@media (max-width: 768px) {
    .popup-content {
        width: 95%;
        margin: 10px;
    }
    .popup-header {
        padding: 15px;
    }
    .popup-header h3 {
        font-size: 1.3rem;
    }
    .popup-body {
        padding: 20px;
    }
    .popup-body .social-links {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    .popup-body .social-link {
        padding: 12px 15px;
    }
    .popup-body .social-link i {
        font-size: 1.3rem;
    }
    .popup-body .social-link span {
        font-size: 0.95rem;
    }
}