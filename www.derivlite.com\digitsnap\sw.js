const CACHE_NAME = 'digitsnap-v1.0.0';
const urlsToCache = [
    '/digitsnap/',
    '/digitsnap/index.html',
    '/digitsnap/styles.css',
    '/digitsnap/script.js',
    '/digitsnap/manifest.json',
    '/digitsnap/icons/icon-192x192.png',
    '/digitsnap/icons/icon-512x512.png',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
    'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap'
];

// Install event - cache resources
self.addEventListener('install', (event) => {
    console.log('[ServiceWorker] Install');
    event.waitUntil(
        caches.open(CACHE_NAME)
        .then((cache) => {
            console.log('[ServiceWorker] Caching app shell');
            return cache.addAll(urlsToCache);
        })
        .then(() => {
            console.log('[ServiceWorker] Skip waiting');
            return self.skipWaiting();
        })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('[ServiceWorker] Activate');
    event.waitUntil(
        caches.keys().then((cacheNames) => {
            return Promise.all(
                cacheNames.map((cacheName) => {
                    if (cacheName !== CACHE_NAME) {
                        console.log('[ServiceWorker] Removing old cache', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('[ServiceWorker] Claiming clients');
            return self.clients.claim();
        })
    );
});

// Fetch event - serve from cache, fallback to network
self.addEventListener('fetch', (event) => {
    console.log('[ServiceWorker] Fetch', event.request.url);

    // Skip non-GET requests
    if (event.request.method !== 'GET') {
        return;
    }

    // Skip requests to external domains (like Deriv API)
    if (!event.request.url.startsWith(self.location.origin) &&
        !event.request.url.includes('cdnjs.cloudflare.com') &&
        !event.request.url.includes('fonts.googleapis.com')) {
        return;
    }

    event.respondWith(
        caches.match(event.request)
        .then((response) => {
            // Return cached version or fetch from network
            if (response) {
                console.log('[ServiceWorker] Found in cache', event.request.url);
                return response;
            }

            console.log('[ServiceWorker] Fetching from network', event.request.url);
            return fetch(event.request).then((response) => {
                // Check if we received a valid response
                if (!response || response.status !== 200 || response.type !== 'basic') {
                    return response;
                }

                // Clone the response as it's a stream and can only be consumed once
                const responseToCache = response.clone();

                caches.open(CACHE_NAME)
                    .then((cache) => {
                        cache.put(event.request, responseToCache);
                    });

                return response;
            });
        })
        .catch((error) => {
            console.log('[ServiceWorker] Fetch failed', error);
            // Return offline page if available
            if (event.request.destination === 'document') {
                return caches.match('/digitsnap/index.html');
            }
        })
    );
});

// Background sync for offline actions
self.addEventListener('sync', (event) => {
    console.log('[ServiceWorker] Background sync', event.tag);

    if (event.tag === 'background-sync') {
        event.waitUntil(
            // Handle background sync logic here
            console.log('[ServiceWorker] Background sync completed')
        );
    }
});

// Push notification handling
self.addEventListener('push', (event) => {
    console.log('[ServiceWorker] Push received');

    const options = {
        body: event.data ? event.data.text() : 'New market update available',
        icon: '/digitsnap/icons/icon-192x192.png',
        badge: '/digitsnap/icons/icon-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            url: '/digitsnap/'
        },
        actions: [{
                action: 'view',
                title: 'View Analysis'
            },
            {
                action: 'dismiss',
                title: 'Dismiss'
            }
        ]
    };

    event.waitUntil(
        self.registration.showNotification('DigitSnap', options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
    console.log('[ServiceWorker] Notification click received');

    event.notification.close();

    if (event.action === 'view') {
        event.waitUntil(
            clients.openWindow('/digitsnap/')
        );
    }
});