// Derivlite Platform JavaScript
console.log('Script.js loaded successfully!'); // Debug message

// Mobile Navigation Toggle
document.addEventListener('DOMContentLoaded', function() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');

    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }

    // Close mobile menu when clicking on a link
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });
});

// Smooth Scrolling for Navigation Links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth'
            });
        }
    });
});

// Customer Support Widget Functionality
let isSupportChatOpen = false;

function toggleSupportChat() {
    console.log('toggleSupportChat called'); // Debug log
    const supportChat = document.getElementById('supportChat');
    const supportBadge = document.querySelector('.support-badge');

    if (!supportChat) {
        console.error('supportChat element not found');
        return;
    }

    if (isSupportChatOpen) {
        supportChat.classList.remove('active');
        isSupportChatOpen = false;
        console.log('Chat closed');
    } else {
        supportChat.classList.add('active');
        isSupportChatOpen = true;
        console.log('Chat opened');
        // Hide the notification badge when chat is opened
        if (supportBadge) {
            supportBadge.style.display = 'none';
        }
    }
}

function sendQuickMessage(message) {
    addMessageToChat(message, true);

    // Simulate support response
    setTimeout(() => {
        let response = "";
        switch (message) {
            case 'How do I get started?':
                response = "Hey! So excited you want to jump in! 😊 It's actually super simple - just hit that 'Get Started' button, fill out the quick signup (literally takes like 2 minutes), then you can deposit as little as $50 to begin. Once that's done, you'll have full access to DigitSnap and can start trading right away! I can walk you through it if you want?";
                break;
            case 'Tell me about DigitSnap':
                response = "Oh man, DigitSnap is seriously incredible! 🚀 We're hitting 95% accuracy consistently - I know that sounds too good to be true but our users are making real money with it daily. It basically analyzes the market in real-time and gives you super clear signals on when to enter trades. Works whether you're completely new to this or you've been trading for years. Want me to show you how it works?";
                break;
            case 'Pricing information':
                response = "Sure thing! So we keep it simple - it's just $250 one-time payment for everything. No monthly fees or subscriptions, you pay once and get lifetime access to DigitSnap, all the features, priority support, the works. Pretty amazing deal considering what you get!";
                break;
            default:
                response = "Hey there! What's up? I'm here to help with whatever you need to know about Derivlite! 😊";
        }
        addMessageToChat(response, false);
    }, 1500);
}

function sendMessage() {
    const chatInput = document.getElementById('chatInput');
    const message = chatInput.value.trim();

    if (message) {
        addMessageToChat(message, true);
        chatInput.value = '';

        // Check for contact-related keywords
        const contactKeywords = ['call', 'whatsapp', 'telegram', 'contact', 'phone', 'number', 'reach', 'talk', 'speak'];
        const hasContactKeyword = contactKeywords.some(keyword =>
            message.toLowerCase().includes(keyword.toLowerCase())
        );

        setTimeout(() => {
            let response = "";

            // Show typing indicator before response
            showTypingIndicator();

            setTimeout(() => {
                hideTypingIndicator();

                if (hasContactKeyword) {
                    response = `Oh, you want to chat directly? Perfect! 😊 Here's how you can reach us:<br><br>WhatsApp is probably the fastest - just hit me up at <a href="https://wa.me/16134957545" target="_blank" style="color: #FFD700;">+16134957545</a><br><br>Or if you're more of a Telegram person, find us at <a href="https://t.me/binary_blueprint" target="_blank" style="color: #FFD700;">@binary_blueprint</a><br><br>We're online 24/7 and usually get back to people in like 5 minutes or less. Which one works better for you?`;
                } else {
                    // Generate contextual responses based on message content
                    const lowerMessage = message.toLowerCase();

                    if (lowerMessage.includes('price') || lowerMessage.includes('cost') || lowerMessage.includes('fee')) {
                        response = "Ah pricing! Super straightforward - it's $250 one-time payment and you're set for life! No monthly subscriptions or recurring charges, just pay once and get full access to DigitSnap, all features, priority support, everything forever. Honestly such a steal for what you get!";
                    } else if (lowerMessage.includes('discount') || lowerMessage.includes('deal') || lowerMessage.includes('special') || lowerMessage.includes('offer')) {
                        response = "Ooh, you're asking about discounts! 😉 We actually do have some special offers running, but I can't share them here in the chat. Hit us up on WhatsApp at <a href='https://wa.me/16134957545' target='_blank' style='color: #FFD700;'>+16134957545</a> and mention you're interested in a discount - the team there can hook you up with something good!";
                    } else if (lowerMessage.includes('demo') || lowerMessage.includes('test') || lowerMessage.includes('try')) {
                        response = "Want to see DigitSnap in action? I can hook you up with a live demo where one of our experts walks you through exactly how it works and shows you real trades. Pretty cool to see the 95% accuracy happening live! Want me to set that up for you?";
                    } else if (lowerMessage.includes('accuracy') || lowerMessage.includes('success') || lowerMessage.includes('rate')) {
                        response = "Dude, the 95% accuracy thing still blows my mind! Like, I've been here for months and it's consistently hitting those numbers. We had it independently verified too because even we were like 'this can't be real' at first 😅 Want to see some recent results?";
                    } else if (lowerMessage.includes('support') || lowerMessage.includes('help') || lowerMessage.includes('assist')) {
                        response = "That's literally what I'm here for! 😊 You can reach us anytime - I'm here on chat, we've got WhatsApp, Telegram, whatever works for you. Usually respond super quick too. What do you need help with specifically?";
                    } else if (lowerMessage.includes('safe') || lowerMessage.includes('secure') || lowerMessage.includes('trust')) {
                        response = "Totally get why you'd ask that - there's so much sketchy stuff out there! We're fully regulated, use bank-level security, and your funds are kept separate from our business money. Been around for years with thousands of happy users. Want me to send you some verification info?";
                    } else if (lowerMessage.includes('withdraw') || lowerMessage.includes('deposit') || lowerMessage.includes('payment')) {
                        response = "Super easy! You can use pretty much any payment method - cards, bank transfer, crypto, PayPal. Deposits show up instantly and withdrawals usually happen within 24 hours. Only need $50 to start which is pretty reasonable. Any specific payment method you're wondering about?";
                    } else if (lowerMessage.includes('beginner') || lowerMessage.includes('new') || lowerMessage.includes('learn')) {
                        response = "Perfect timing to start! DigitSnap is honestly designed for people who've never traded before. It walks you through everything, you can practice with fake money first, and there's even a personal coach if you want one. You literally don't need to know anything about trading to make money with this.";
                    } else {
                        // More natural general responses
                        const naturalResponses = [
                            "Hmm, tell me more about what you're thinking? I want to make sure I give you the right info!",
                            "Interesting! What specifically about Derivlite caught your attention?",
                            "I'm all ears! What would you like to know?",
                            "Sure thing! What's on your mind?",
                            "Cool! How can I help you out today?",
                            "What's up? Fire away with any questions you've got!"
                        ];
                        response = naturalResponses[Math.floor(Math.random() * naturalResponses.length)];
                    }
                }

                addMessageToChat(response, false);
            }, 800 + Math.random() * 800); // Typing delay
        }, 1000 + Math.random() * 1000); // Random delay between 1-2 seconds for more natural feel
    }
}

function showTypingIndicator() {
    const chatBody = document.querySelector('.chat-body');
    const typingDiv = document.createElement('div');
    typingDiv.className = 'chat-message support-message typing-indicator';
    typingDiv.id = 'typingIndicator';

    typingDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-user-tie"></i>
        </div>
        <div class="message-content">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    `;

    chatBody.appendChild(typingDiv);
    chatBody.scrollTop = chatBody.scrollHeight;
}

function hideTypingIndicator() {
    const typingIndicator = document.getElementById('typingIndicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

function addMessageToChat(message, isUser) {
    const chatBody = document.querySelector('.chat-body');
    const messageDiv = document.createElement('div');
    messageDiv.className = `chat-message ${isUser ? 'user-message' : 'support-message'}`;

    // Add timestamp for more realism
    const now = new Date();
    const time = now.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit'
    });

    messageDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas ${isUser ? 'fa-user' : 'fa-user-tie'}"></i>
        </div>
        <div class="message-content">
            <div class="message-text">${message}</div>
            <div class="message-time">${time}</div>
        </div>
    `;

    chatBody.appendChild(messageDiv);
    chatBody.scrollTop = chatBody.scrollHeight;

    // Add typing indicator for support messages
    if (!isUser) {
        // Add some visual feedback that the support is "typing"
        const lastMessage = chatBody.lastElementChild;
        lastMessage.style.opacity = '0';
        lastMessage.style.transform = 'translateY(10px)';

        setTimeout(() => {
            lastMessage.style.transition = 'all 0.3s ease';
            lastMessage.style.opacity = '1';
            lastMessage.style.transform = 'translateY(0)';
        }, 100);
    }
}

function showContactInfo() {
    const contactMessage = `Hey! So here's how you can get hold of us 😊<br><br>WhatsApp is super quick: <a href="https://wa.me/16134957545" target="_blank" style="color: #FFD700; text-decoration: none;">+16134957545</a> - just click and message us!<br><br>Or Telegram if that's your thing: <a href="https://t.me/binary_blueprint" target="_blank" style="color: #FFD700; text-decoration: none;">@binary_blueprint</a><br><br>Email works too: <a href="mailto:<EMAIL>" style="color: #FFD700; text-decoration: none;"><EMAIL></a><br><br>We're online basically all the time and get back to people crazy fast. What works best for you?`;

    addMessageToChat(contactMessage, false);
}

// Handle Enter key in chat input
document.addEventListener('DOMContentLoaded', function() {
    const chatInput = document.getElementById('chatInput');
    if (chatInput) {
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    }
});

// Payment Modal Functions (if you have payment modals)
function openUSDTModal() {
    const modal = document.getElementById('usdtModal');
    if (modal) {
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
}

function closeUSDTModal() {
    const modal = document.getElementById('usdtModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

function confirmUSDTPayment() {
    // Handle payment confirmation logic here
    alert('Payment confirmation received! Our team will verify your transaction shortly.');
    closeUSDTModal();
}

// Close modals when clicking outside
document.addEventListener('click', function(e) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (e.target === modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    });
});

// Add some interactive effects
document.addEventListener('DOMContentLoaded', function() {
    // Add floating animation to the support button
    const supportToggle = document.querySelector('.support-toggle');
    if (supportToggle) {
        setInterval(() => {
            if (!isSupportChatOpen) {
                supportToggle.style.animation = 'none';
                setTimeout(() => {
                    supportToggle.style.animation = 'pulse 2s infinite';
                }, 10);
            }
        }, 10000); // Pulse every 10 seconds to draw attention
    }
});

// Initialize chat with a welcome message
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        const supportBadge = document.querySelector('.support-badge');
        if (supportBadge) {
            supportBadge.style.display = 'flex';
        }
    }, 3000); // Show notification badge after 3 seconds
});