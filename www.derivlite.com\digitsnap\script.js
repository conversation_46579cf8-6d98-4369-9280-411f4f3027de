// DigitSnap Trading App JavaScript

class DigitSnapApp {
    constructor() {
        this.isConnected = false;
        this.balance = 1000.00; // Demo balance
        this.signals = [];
        this.trades = [];
        this.winRate = 95.2;
        this.todayProfit = 0;
        
        this.init();
    }

    init() {
        this.showLoadingScreen();
        this.setupEventListeners();
        this.simulateInitialization();
    }

    showLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        const app = document.getElementById('app');
        
        // Simulate loading time
        setTimeout(() => {
            loadingScreen.style.display = 'none';
            app.style.display = 'block';
            this.updateUI();
            this.startDataSimulation();
        }, 3000);
    }

    setupEventListeners() {
        // FAB button
        const tradeFab = document.getElementById('tradeFab');
        if (tradeFab) {
            tradeFab.addEventListener('click', () => this.showTradingPanel());
        }

        // Refresh button
        const refreshBtn = document.getElementById('refreshMarket');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshMarketData());
        }

        // Settings button
        const settingsBtn = document.getElementById('settingsBtn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => this.showSettings());
        }

        // Asset and timeframe selectors
        const assetSelect = document.getElementById('assetSelect');
        const timeframeSelect = document.getElementById('timeframeSelect');
        
        if (assetSelect) {
            assetSelect.addEventListener('change', () => this.updateChart());
        }
        
        if (timeframeSelect) {
            timeframeSelect.addEventListener('change', () => this.updateChart());
        }

        // Signal filter
        const signalFilter = document.getElementById('signalFilter');
        if (signalFilter) {
            signalFilter.addEventListener('change', () => this.filterSignals());
        }
    }

    simulateInitialization() {
        // Simulate connection to trading servers
        setTimeout(() => {
            this.isConnected = true;
            this.updateConnectionStatus();
            this.generateInitialSignals();
        }, 2000);
    }

    updateConnectionStatus() {
        const statusElement = document.getElementById('connectionStatus');
        if (statusElement) {
            const dot = statusElement.querySelector('.status-dot');
            const text = statusElement.querySelector('span');
            
            if (this.isConnected) {
                dot.style.background = '#4CAF50';
                text.textContent = 'Connected';
            } else {
                dot.style.background = '#F44336';
                text.textContent = 'Disconnected';
            }
        }
    }

    updateUI() {
        // Update balance
        const balanceElement = document.getElementById('accountBalance');
        if (balanceElement) {
            balanceElement.textContent = `$${this.balance.toFixed(2)}`;
        }

        // Update win rate
        const winRateElement = document.getElementById('winRate');
        if (winRateElement) {
            winRateElement.textContent = `${this.winRate}%`;
        }

        // Update today's profit
        const profitElement = document.getElementById('todayProfit');
        if (profitElement) {
            profitElement.textContent = `+$${this.todayProfit.toFixed(2)}`;
        }

        // Update active signals count
        const signalsElement = document.getElementById('activeSignals');
        if (signalsElement) {
            signalsElement.textContent = this.signals.length;
        }
    }

    generateInitialSignals() {
        const assets = ['EUR/USD', 'GBP/JPY', 'AUD/CAD', 'USD/JPY', 'GBP/USD'];
        const directions = ['Call', 'Put'];
        const confidences = ['high', 'medium'];
        
        // Generate 3-5 random signals
        const signalCount = Math.floor(Math.random() * 3) + 3;
        
        for (let i = 0; i < signalCount; i++) {
            const signal = {
                id: Date.now() + i,
                asset: assets[Math.floor(Math.random() * assets.length)],
                direction: directions[Math.floor(Math.random() * directions.length)],
                confidence: confidences[Math.floor(Math.random() * confidences.length)],
                timestamp: new Date(),
                accuracy: Math.floor(Math.random() * 10) + 90 // 90-99%
            };
            
            this.signals.push(signal);
        }
        
        this.renderSignals();
        this.updateUI();
    }

    renderSignals() {
        const signalsList = document.getElementById('signalsList');
        if (!signalsList) return;

        if (this.signals.length === 0) {
            signalsList.innerHTML = `
                <div class="no-signals">
                    <i class="fas fa-search"></i>
                    <p>Scanning markets for opportunities...</p>
                </div>
            `;
            return;
        }

        signalsList.innerHTML = this.signals.map(signal => `
            <div class="signal-item">
                <div class="signal-info">
                    <div class="signal-asset">${signal.asset}</div>
                    <div class="signal-direction">
                        <i class="fas fa-arrow-${signal.direction === 'Call' ? 'up' : 'down'}"></i>
                        ${signal.direction} - ${signal.accuracy}% confidence
                    </div>
                </div>
                <div class="signal-confidence confidence-${signal.confidence}">
                    ${signal.confidence.toUpperCase()}
                </div>
            </div>
        `).join('');
    }

    filterSignals() {
        const filter = document.getElementById('signalFilter').value;
        let filteredSignals = this.signals;

        if (filter !== 'all') {
            filteredSignals = this.signals.filter(signal => signal.confidence === filter);
        }

        // Temporarily store original signals and render filtered ones
        const originalSignals = this.signals;
        this.signals = filteredSignals;
        this.renderSignals();
        this.signals = originalSignals;
    }

    startDataSimulation() {
        // Simulate real-time data updates
        setInterval(() => {
            this.updateMarketData();
        }, 5000); // Update every 5 seconds

        // Generate new signals periodically
        setInterval(() => {
            if (Math.random() > 0.7) { // 30% chance
                this.generateNewSignal();
            }
        }, 15000); // Check every 15 seconds
    }

    updateMarketData() {
        // Simulate small balance changes
        const change = (Math.random() - 0.5) * 20; // -10 to +10
        this.balance += change;
        this.todayProfit += change;

        // Simulate win rate fluctuation
        this.winRate += (Math.random() - 0.5) * 0.2; // Small fluctuation
        this.winRate = Math.max(90, Math.min(99, this.winRate)); // Keep between 90-99%

        this.updateUI();
    }

    generateNewSignal() {
        const assets = ['EUR/USD', 'GBP/JPY', 'AUD/CAD', 'USD/JPY', 'GBP/USD'];
        const directions = ['Call', 'Put'];
        const confidences = ['high', 'medium'];
        
        const signal = {
            id: Date.now(),
            asset: assets[Math.floor(Math.random() * assets.length)],
            direction: directions[Math.floor(Math.random() * directions.length)],
            confidence: confidences[Math.floor(Math.random() * confidences.length)],
            timestamp: new Date(),
            accuracy: Math.floor(Math.random() * 10) + 90
        };

        // Remove oldest signal if we have too many
        if (this.signals.length >= 5) {
            this.signals.shift();
        }

        this.signals.push(signal);
        this.renderSignals();
        this.updateUI();

        // Show notification for new signal
        this.showNotification(`New ${signal.confidence} confidence signal for ${signal.asset}`, 'info');
    }

    refreshMarketData() {
        const refreshBtn = document.getElementById('refreshMarket');
        if (refreshBtn) {
            const icon = refreshBtn.querySelector('i');
            icon.style.animation = 'spin 1s linear';
            
            setTimeout(() => {
                icon.style.animation = '';
                this.generateInitialSignals();
                this.showNotification('Market data refreshed', 'success');
            }, 1000);
        }
    }

    updateChart() {
        const asset = document.getElementById('assetSelect').value;
        const timeframe = document.getElementById('timeframeSelect').value;
        
        // Simulate chart update
        const chartContainer = document.getElementById('chartContainer');
        if (chartContainer) {
            chartContainer.innerHTML = `
                <div class="chart-placeholder">
                    <i class="fas fa-chart-line"></i>
                    <p>Loading ${asset} chart (${timeframe})...</p>
                </div>
            `;
            
            setTimeout(() => {
                chartContainer.innerHTML = `
                    <div class="chart-placeholder">
                        <i class="fas fa-chart-line"></i>
                        <p>${asset} - ${timeframe} chart loaded</p>
                        <small style="color: var(--medium-gray);">Chart integration coming soon</small>
                    </div>
                `;
            }, 1500);
        }
    }

    showTradingPanel() {
        this.showNotification('Trading panel feature coming soon!', 'info');
    }

    showSettings() {
        this.showNotification('Settings panel coming soon!', 'info');
    }

    showNotification(message, type = 'info') {
        const container = document.getElementById('notificationContainer');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}"></i>
            <span>${message}</span>
        `;

        container.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.digitSnapApp = new DigitSnapApp();
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DigitSnapApp;
}
