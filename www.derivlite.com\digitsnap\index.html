<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DigitSnap - Advanced Trading Tool</title>
    <meta name="description" content="DigitSnap - Professional trading tool with 95% accuracy rate. Real-time market analysis and trading signals.">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#FFD700">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="DigitSnap">
    
    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="icons/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icons/icon-16x16.png">
    <link rel="apple-touch-icon" sizes="192x192" href="icons/icon-192x192.png">
    <link rel="apple-touch-icon" sizes="512x512" href="icons/icon-512x512.png">
    
    <!-- Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <i class="fas fa-bullseye"></i>
            </div>
            <h2>DigitSnap</h2>
            <div class="loading-spinner"></div>
            <p>Initializing trading engine...</p>
        </div>
    </div>

    <!-- Main App Container -->
    <div id="app" class="app-container" style="display: none;">
        <!-- Header -->
        <header class="app-header">
            <div class="header-left">
                <div class="app-logo">
                    <i class="fas fa-bullseye"></i>
                    <span>DigitSnap</span>
                </div>
                <div class="connection-status">
                    <div class="status-indicator" id="connectionStatus">
                        <div class="status-dot"></div>
                        <span>Connected</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="account-balance">
                    <span class="balance-label">Balance:</span>
                    <span class="balance-amount" id="accountBalance">$0.00</span>
                </div>
                <button class="settings-btn" id="settingsBtn">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Grid -->
            <div class="dashboard-grid">
                <!-- Market Overview -->
                <div class="dashboard-card market-overview">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-line"></i> Market Overview</h3>
                        <div class="card-actions">
                            <button class="refresh-btn" id="refreshMarket">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="market-stats">
                            <div class="stat-item">
                                <span class="stat-label">Active Signals</span>
                                <span class="stat-value" id="activeSignals">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Win Rate</span>
                                <span class="stat-value success" id="winRate">95.2%</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Today's Profit</span>
                                <span class="stat-value profit" id="todayProfit">+$0.00</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Trading Signals -->
                <div class="dashboard-card trading-signals">
                    <div class="card-header">
                        <h3><i class="fas fa-signal"></i> Live Signals</h3>
                        <div class="signal-filter">
                            <select id="signalFilter">
                                <option value="all">All Signals</option>
                                <option value="high">High Confidence</option>
                                <option value="medium">Medium Confidence</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="signals-list" id="signalsList">
                            <div class="no-signals">
                                <i class="fas fa-search"></i>
                                <p>Scanning markets for opportunities...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chart Analysis -->
                <div class="dashboard-card chart-analysis">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-area"></i> Chart Analysis</h3>
                        <div class="chart-controls">
                            <select id="assetSelect">
                                <option value="EURUSD">EUR/USD</option>
                                <option value="GBPJPY">GBP/JPY</option>
                                <option value="AUDCAD">AUD/CAD</option>
                                <option value="USDJPY">USD/JPY</option>
                            </select>
                            <select id="timeframeSelect">
                                <option value="1m">1 Min</option>
                                <option value="5m">5 Min</option>
                                <option value="15m">15 Min</option>
                                <option value="1h">1 Hour</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="chart-container" id="chartContainer">
                            <div class="chart-placeholder">
                                <i class="fas fa-chart-line"></i>
                                <p>Chart will load here</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Trade History -->
                <div class="dashboard-card trade-history">
                    <div class="card-header">
                        <h3><i class="fas fa-history"></i> Recent Trades</h3>
                        <button class="view-all-btn" id="viewAllTrades">View All</button>
                    </div>
                    <div class="card-content">
                        <div class="trades-list" id="tradesList">
                            <div class="no-trades">
                                <i class="fas fa-chart-bar"></i>
                                <p>No trades yet. Start trading to see history.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Trading Panel -->
        <div class="trading-panel" id="tradingPanel">
            <div class="panel-header">
                <h3>Quick Trade</h3>
                <button class="close-panel" id="closeTradingPanel">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="panel-content">
                <div class="trade-form">
                    <div class="form-group">
                        <label>Asset</label>
                        <select id="tradeAsset">
                            <option value="EURUSD">EUR/USD</option>
                            <option value="GBPJPY">GBP/JPY</option>
                            <option value="AUDCAD">AUD/CAD</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Amount</label>
                        <input type="number" id="tradeAmount" value="10" min="1" max="1000">
                    </div>
                    <div class="form-group">
                        <label>Direction</label>
                        <div class="direction-buttons">
                            <button class="direction-btn call-btn" id="callBtn">
                                <i class="fas fa-arrow-up"></i>
                                Call (Higher)
                            </button>
                            <button class="direction-btn put-btn" id="putBtn">
                                <i class="fas fa-arrow-down"></i>
                                Put (Lower)
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Duration</label>
                        <select id="tradeDuration">
                            <option value="60">1 Minute</option>
                            <option value="300">5 Minutes</option>
                            <option value="900">15 Minutes</option>
                            <option value="3600">1 Hour</option>
                        </select>
                    </div>
                    <button class="execute-trade-btn" id="executeTrade">
                        <i class="fas fa-play"></i>
                        Execute Trade
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Action Button -->
    <button class="fab" id="tradeFab" title="Quick Trade">
        <i class="fas fa-plus"></i>
    </button>

    <!-- Notification Container -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- Scripts -->
    <script src="script.js"></script>
    
    <!-- Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('./sw.js')
                    .then((registration) => {
                        console.log('SW registered: ', registration);
                    })
                    .catch((registrationError) => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
</body>
</html>
